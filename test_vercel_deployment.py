#!/usr/bin/env python3
"""
Vercel Frontend Deployment Test Script
Tests the deployed Vue2 bookmark manager frontend on Vercel
"""

import requests
import json
import time
from datetime import datetime

# Vercel deployment URLs
FRONTEND_URL = "https://catai-bookmark-main-v1-7djdvks3h-catais-projects.vercel.app"
BACKEND_URL = "https://catai-bookmark-api-v1-production.up.railway.app"

class VercelFrontendTester:
    def __init__(self, frontend_url, backend_url):
        self.frontend_url = frontend_url.rstrip('/')
        self.backend_url = backend_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Vercel-Frontend-Tester/1.0'
        })
    
    def print_status(self, test_name, status, message=""):
        """Print test status with formatting"""
        status_symbol = "✅" if status else "❌"
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {status_symbol} {test_name}: {message}")
    
    def test_frontend_accessibility(self):
        """Test if frontend is accessible"""
        try:
            response = self.session.get(self.frontend_url, timeout=15)
            if response.status_code == 200:
                # Check if it's a Vue.js app
                content = response.text.lower()
                if 'vue' in content or 'app' in content:
                    self.print_status("Frontend Accessibility", True, f"HTTP {response.status_code} - Vue app loaded")
                    return True
                else:
                    self.print_status("Frontend Accessibility", True, f"HTTP {response.status_code} - Page loaded")
                    return True
            else:
                self.print_status("Frontend Accessibility", False, f"HTTP {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.print_status("Frontend Accessibility", False, f"Connection error: {str(e)}")
            return False
    
    def test_backend_connectivity(self):
        """Test backend API connectivity"""
        try:
            response = self.session.get(f"{self.backend_url}/api/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.print_status("Backend Connectivity", True, f"API healthy: {data.get('status', 'unknown')}")
                return True
            else:
                self.print_status("Backend Connectivity", False, f"HTTP {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.print_status("Backend Connectivity", False, f"Error: {str(e)}")
            return False
    
    def test_cors_configuration(self):
        """Test CORS configuration between frontend and backend"""
        try:
            # Test preflight request
            headers = {
                'Origin': self.frontend_url,
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type'
            }
            response = self.session.options(f"{self.backend_url}/api/bookmarks", headers=headers, timeout=10)
            
            cors_origin = response.headers.get('Access-Control-Allow-Origin')
            if cors_origin == '*' or cors_origin == self.frontend_url:
                self.print_status("CORS Configuration", True, f"CORS allows origin: {cors_origin}")
                return True
            else:
                self.print_status("CORS Configuration", False, f"CORS origin: {cors_origin}")
                return False
        except requests.exceptions.RequestException as e:
            self.print_status("CORS Configuration", False, f"Error: {str(e)}")
            return False
    
    def test_api_endpoints_from_frontend(self):
        """Test API endpoints that frontend would call"""
        endpoints = [
            ("/api/bookmarks", "GET", "Bookmarks API"),
            ("/api/folders", "GET", "Folders API"),
            ("/api/tags", "GET", "Tags API"),
            ("/api/stats", "GET", "Stats API")
        ]
        
        results = []
        for endpoint, method, name in endpoints:
            try:
                headers = {'Origin': self.frontend_url}
                response = self.session.request(
                    method, 
                    f"{self.backend_url}{endpoint}", 
                    headers=headers,
                    timeout=10
                )
                if response.status_code in [200, 404]:  # 200 for data, 404 for no data
                    self.print_status(name, True, f"HTTP {response.status_code}")
                    results.append(True)
                else:
                    self.print_status(name, False, f"HTTP {response.status_code}")
                    results.append(False)
            except requests.exceptions.RequestException as e:
                self.print_status(name, False, f"Error: {str(e)}")
                results.append(False)
        
        return all(results)
    
    def test_frontend_static_assets(self):
        """Test if frontend static assets are loading"""
        try:
            # Test main page first
            response = self.session.get(self.frontend_url, timeout=10)
            if response.status_code != 200:
                self.print_status("Static Assets", False, "Main page not accessible")
                return False
            
            # Look for common static asset references
            content = response.text
            assets_found = []
            
            if '.js' in content:
                assets_found.append('JavaScript')
            if '.css' in content:
                assets_found.append('CSS')
            if 'static/' in content:
                assets_found.append('Static files')
            
            if assets_found:
                self.print_status("Static Assets", True, f"Found: {', '.join(assets_found)}")
                return True
            else:
                self.print_status("Static Assets", True, "Basic HTML loaded")
                return True
                
        except requests.exceptions.RequestException as e:
            self.print_status("Static Assets", False, f"Error: {str(e)}")
            return False
    
    def test_spa_routing(self):
        """Test Single Page Application routing"""
        test_routes = [
            "/",
            "/bookmarks",
            "/folders",
            "/settings"
        ]
        
        results = []
        for route in test_routes:
            try:
                response = self.session.get(f"{self.frontend_url}{route}", timeout=10)
                if response.status_code == 200:
                    self.print_status(f"SPA Route {route}", True, "Route accessible")
                    results.append(True)
                else:
                    self.print_status(f"SPA Route {route}", False, f"HTTP {response.status_code}")
                    results.append(False)
            except requests.exceptions.RequestException as e:
                self.print_status(f"SPA Route {route}", False, f"Error: {str(e)}")
                results.append(False)
        
        return any(results)  # At least one route should work
    
    def run_all_tests(self):
        """Run all tests"""
        print(f"\n🚀 Vercel Frontend Deployment Test")
        print(f"📍 Frontend URL: {self.frontend_url}")
        print(f"🔗 Backend URL: {self.backend_url}")
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        tests = [
            ("Frontend Accessibility", self.test_frontend_accessibility),
            ("Backend Connectivity", self.test_backend_connectivity),
            ("CORS Configuration", self.test_cors_configuration),
            ("API Endpoints", self.test_api_endpoints_from_frontend),
            ("Static Assets", self.test_frontend_static_assets),
            ("SPA Routing", self.test_spa_routing)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🔍 Running {test_name}...")
            result = test_func()
            results.append(result)
            time.sleep(1)  # Small delay between tests
        
        # Summary
        print("\n" + "=" * 70)
        passed = sum(results)
        total = len(results)
        
        if passed == total:
            print(f"🎉 All tests passed! ({passed}/{total})")
            print("✅ Your Vercel deployment is working perfectly!")
            print(f"🌐 You can now use your bookmark manager at: {self.frontend_url}")
        elif passed >= total * 0.7:  # 70% pass rate
            print(f"⚠️  Most tests passed ({passed}/{total})")
            print("✅ Your deployment is mostly working!")
            print(f"🌐 You can try using your bookmark manager at: {self.frontend_url}")
        else:
            print(f"❌ Several tests failed ({passed}/{total})")
            print("⚠️  Please check the failed tests above")
        
        print(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return passed >= total * 0.7

def main():
    """Main function"""
    print("Vercel Frontend Deployment Tester")
    print("=" * 40)
    
    tester = VercelFrontendTester(FRONTEND_URL, BACKEND_URL)
    success = tester.run_all_tests()
    
    if success:
        print(f"\n🎊 Congratulations! Your full-stack bookmark manager is deployed and working!")
        print(f"📱 Frontend: {FRONTEND_URL}")
        print(f"🔧 Backend: {BACKEND_URL}")
    
    return success

if __name__ == "__main__":
    main()
