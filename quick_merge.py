#!/usr/bin/env python3
"""
Quick merge solution - Export current data for manual merge
"""

import requests
import json
from datetime import datetime

# API URL
RAILWAY_API = "https://catai-bookmark-api-v1-production.up.railway.app/api"

def quick_export():
    """Export current database data"""
    
    print("📤 Quick Database Export")
    print("=" * 30)
    
    try:
        # Get current bookmarks
        response = requests.get(f"{RAILWAY_API}/bookmarks?limit=1000", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            bookmarks = data.get('data', {}).get('bookmarks', [])
            
            print(f"✅ Found {len(bookmarks)} bookmarks in current database")
            
            # Show all bookmarks
            if bookmarks:
                print(f"\n📚 Current database contents:")
                for i, bookmark in enumerate(bookmarks, 1):
                    print(f"  {i}. {bookmark.get('title', 'No title')}")
                    print(f"     URL: {bookmark.get('url', 'No URL')}")
                    print(f"     Created: {bookmark.get('created_at', 'Unknown')}")
                    print()
            
            # Export to file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"current_bookmarks_{timestamp}.json"
            
            export_data = {
                "export_info": {
                    "database_name": "CataPage_test_wwtmw20_Chrome (current)",
                    "export_date": datetime.now().isoformat(),
                    "total_bookmarks": len(bookmarks),
                    "source": "Railway API"
                },
                "bookmarks": bookmarks
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Data exported to: {filename}")
            
            # Create merge template
            merge_template = {
                "instructions": [
                    "This file contains bookmarks from the CURRENT database (the one API is using)",
                    "To merge with your ORIGINAL database:",
                    "1. Add your original database bookmarks to 'original_database_bookmarks' below",
                    "2. Save this file",
                    "3. Use the import script to merge them"
                ],
                "current_database_bookmarks": bookmarks,
                "original_database_bookmarks": {
                    "note": "Add your original database bookmarks here",
                    "format": "Same format as current_database_bookmarks",
                    "bookmarks": []
                },
                "merge_settings": {
                    "remove_duplicates": True,
                    "duplicate_check_field": "url",
                    "prefer_original": True
                }
            }
            
            template_file = f"merge_template_{timestamp}.json"
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(merge_template, f, indent=2, ensure_ascii=False)
            
            print(f"📝 Merge template created: {template_file}")
            
            print(f"\n🎯 Next Steps:")
            print(f"1. Check your MongoDB to identify which database has your original data")
            print(f"2. Export your original database bookmarks")
            print(f"3. Add them to {template_file}")
            print(f"4. Run the merge import")
            
            return True
            
        else:
            print(f"❌ Failed to get bookmarks: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    quick_export()
