<template>
  <div class="bookmark-form bg-white rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-xl font-semibold text-gray-900">
        {{ isEditing ? '编辑书签' : '新增书签' }}
      </h2>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <form @submit.prevent="handleSubmit">
      <!-- 标题 -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          标题 <span class="text-red-500">*</span>
        </label>
        <input
          v-model="formData.title"
          type="text"
          placeholder="请输入书签标题"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          :class="{ 'border-red-500': errors.title }"
        />
        <p v-if="errors.title" class="text-red-500 text-sm mt-1">{{ errors.title }}</p>
      </div>

      <!-- URL -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          URL <span class="text-red-500">*</span>
        </label>
        <input
          v-model="formData.url"
          type="url"
          placeholder="https://example.com"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          :class="{ 'border-red-500': errors.url }"
        />
        <p v-if="errors.url" class="text-red-500 text-sm mt-1">{{ errors.url }}</p>
      </div>

      <!-- 标签 -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">标签</label>
        <div class="flex flex-wrap gap-2 mb-2">
          <span
            v-for="(tag, index) in formData.tags"
            :key="index"
            class="inline-flex items-center px-2 py-1 text-sm bg-blue-100 text-blue-800 rounded-full"
          >
            {{ tag }}
            <button
              type="button"
              @click="removeTag(index)"
              class="ml-1 text-blue-600 hover:text-blue-800"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </span>
        </div>
        <div class="flex">
          <input
            v-model="newTag"
            type="text"
            placeholder="输入标签后按回车添加"
            @keyup.enter="addTag"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            type="button"
            @click="addTag"
            class="px-4 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 transition-colors"
          >
            添加
          </button>
        </div>
      </div>

      <!-- 紧迫度和重要度 -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <!-- 紧迫度 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">紧迫度</label>
          <select
            v-model="formData.urgency"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option
              v-for="option in urgencyOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
        </div>

        <!-- 重要度 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">重要度</label>
          <div class="flex items-center space-x-1">
            <button
              v-for="star in 5"
              :key="star"
              type="button"
              @click="formData.importance = star"
              class="text-2xl transition-colors"
              :class="star <= formData.importance ? 'text-yellow-500' : 'text-gray-300'"
            >
              ★
            </button>
            <span class="ml-2 text-sm text-gray-600">({{ formData.importance }}星)</span>
          </div>
        </div>
      </div>

      <!-- 提醒日期 -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">提醒日期</label>
        <input
          v-model="formData.reminder"
          type="date"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      <!-- 备注 -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
        <textarea
          v-model="formData.comment"
          rows="3"
          placeholder="请输入备注信息"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
        ></textarea>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between">
        <div>
          <button
            v-if="isEditing"
            type="button"
            @click="handleDelete"
            class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
            :disabled="loading"
          >
            删除
          </button>
        </div>
        <div class="flex space-x-2">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50"
            :disabled="loading"
          >
            {{ loading ? '保存中...' : (isEditing ? '更新' : '创建') }}
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import { URGENCY_OPTIONS, DEFAULT_BOOKMARK, VALIDATION_RULES } from '../utils/constants'
import { deepClone, isValidUrl, isEmpty } from '../utils/helpers'

export default {
  name: 'BookmarkForm',
  props: {
    bookmark: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: deepClone(DEFAULT_BOOKMARK),
      newTag: '',
      errors: {},
      urgencyOptions: URGENCY_OPTIONS
    }
  },
  computed: {
    isEditing() {
      return !!this.bookmark
    }
  },
  watch: {
    bookmark: {
      handler(newBookmark) {
        if (newBookmark) {
          this.formData = deepClone(newBookmark)
          // 确保tags是数组
          if (!Array.isArray(this.formData.tags)) {
            this.formData.tags = []
          }
        } else {
          this.formData = deepClone(DEFAULT_BOOKMARK)
        }
        this.errors = {}
      },
      immediate: true
    }
  },
  methods: {
    addTag() {
      const tag = this.newTag.trim()
      if (tag && !this.formData.tags.includes(tag)) {
        this.formData.tags.push(tag)
        this.newTag = ''
      }
    },
    
    removeTag(index) {
      this.formData.tags.splice(index, 1)
    },
    
    validateForm() {
      this.errors = {}
      
      // 验证标题
      if (isEmpty(this.formData.title)) {
        this.errors.title = VALIDATION_RULES.title.message
      }
      
      // 验证URL
      if (isEmpty(this.formData.url)) {
        this.errors.url = VALIDATION_RULES.url.message
      } else if (!isValidUrl(this.formData.url)) {
        this.errors.url = VALIDATION_RULES.url.message
      }
      
      return Object.keys(this.errors).length === 0
    },
    
    handleSubmit() {
      if (!this.validateForm()) {
        return
      }
      
      const submitData = deepClone(this.formData)
      
      // 处理日期格式
      if (submitData.reminder === '') {
        submitData.reminder = null
      }
      
      this.$emit('submit', submitData)
    },
    
    handleDelete() {
      if (confirm('确定要删除这个书签吗？')) {
        this.$emit('delete', this.bookmark.id)
      }
    }
  }
}
</script>

<style scoped>
/* 自定义样式 */
.bookmark-form {
  max-height: 80vh;
  overflow-y: auto;
}

/* 星级按钮悬停效果 */
.star-button:hover {
  transform: scale(1.1);
}
</style>

