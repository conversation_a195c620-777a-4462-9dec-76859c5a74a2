#!/usr/bin/env python3
"""
Add test bookmarks to verify frontend-backend connection
"""

import requests
import json

# Backend API URL
API_BASE_URL = "https://catai-bookmark-api-v1-production.up.railway.app/api"

def add_test_bookmarks():
    """Add some test bookmarks to the database"""
    
    test_bookmarks = [
        {
            "title": "Google",
            "url": "https://www.google.com",
            "description": "Search engine",
            "tags": ["search", "google"],
            "folder": "常用网站",
            "urgency": 3,
            "importance": 4
        },
        {
            "title": "GitHub",
            "url": "https://github.com",
            "description": "Code repository platform",
            "tags": ["development", "code", "git"],
            "folder": "开发工具",
            "urgency": 4,
            "importance": 5
        },
        {
            "title": "Stack Overflow",
            "url": "https://stackoverflow.com",
            "description": "Programming Q&A community",
            "tags": ["programming", "help", "community"],
            "folder": "开发工具",
            "urgency": 3,
            "importance": 4
        },
        {
            "title": "Vue.js Documentation",
            "url": "https://vuejs.org",
            "description": "Vue.js official documentation",
            "tags": ["vue", "javascript", "frontend"],
            "folder": "学习资源",
            "urgency": 2,
            "importance": 5
        },
        {
            "title": "Railway Documentation",
            "url": "https://docs.railway.app",
            "description": "Railway deployment platform docs",
            "tags": ["deployment", "railway", "hosting"],
            "folder": "部署工具",
            "urgency": 2,
            "importance": 3
        }
    ]
    
    print("🚀 Adding test bookmarks to your database...")
    print(f"📍 API URL: {API_BASE_URL}")
    print("-" * 50)
    
    session = requests.Session()
    session.headers.update({
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Bookmark-Adder/1.0'
    })
    
    success_count = 0
    
    for i, bookmark in enumerate(test_bookmarks, 1):
        try:
            print(f"{i}. Adding: {bookmark['title']}")
            
            response = session.post(
                f"{API_BASE_URL}/bookmarks",
                json=bookmark,
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                print(f"   ✅ Success: {bookmark['title']}")
                success_count += 1
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                print(f"   Response: {response.text[:100]}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Error: {str(e)}")
    
    print("-" * 50)
    print(f"📊 Results: {success_count}/{len(test_bookmarks)} bookmarks added successfully")
    
    if success_count > 0:
        print("✅ Test data added! Now check your frontend:")
        print("🌐 https://catai-bookmark-main-v1.vercel.app/")
        
        # Verify the data was added
        print("\n🔍 Verifying data...")
        try:
            response = session.get(f"{API_BASE_URL}/bookmarks", timeout=10)
            if response.status_code == 200:
                data = response.json()
                total = data.get('data', {}).get('total', 0)
                print(f"✅ Verification: {total} bookmarks found in database")
            else:
                print(f"❌ Verification failed: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ Verification error: {str(e)}")
    else:
        print("❌ No bookmarks were added. Please check the API connection.")
    
    return success_count > 0

def test_api_connection():
    """Test basic API connection"""
    print("🔍 Testing API connection...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Health Check: {data}")
            return True
        else:
            print(f"❌ API Health Check failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API connection error: {str(e)}")
        return False

def main():
    """Main function"""
    print("📚 Bookmark Manager Test Data Setup")
    print("=" * 50)
    
    # Test API connection first
    if not test_api_connection():
        print("❌ Cannot connect to API. Please check the backend deployment.")
        return False
    
    # Add test bookmarks
    success = add_test_bookmarks()
    
    if success:
        print("\n🎉 Setup complete!")
        print("📱 Now visit your frontend to see the test bookmarks:")
        print("🔗 https://catai-bookmark-main-v1.vercel.app/")
        print("\n💡 You can now test:")
        print("   - View bookmarks list")
        print("   - Search bookmarks")
        print("   - Edit bookmarks")
        print("   - Add new bookmarks")
        print("   - Delete bookmarks")
    
    return success

if __name__ == "__main__":
    main()
