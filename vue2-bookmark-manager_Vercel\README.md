# Vue2 书签管理器

基于Vue2架构的现代化书签管理应用，提供完整的书签管理功能，包括标签分类、搜索筛选、数据同步等。

## 功能特性

### 核心功能
- ✅ **书签管理**: 完整的CRUD操作（创建、读取、更新、删除）
- ✅ **标签系统**: 支持多标签分类和管理
- ✅ **搜索筛选**: 强大的搜索和多维度筛选功能
- ✅ **数据同步**: 支持数据导入导出和云端同步
- ✅ **统计分析**: 可视化的数据统计和图表展示

### 高级功能
- 🔍 **实时搜索**: 支持标题、URL、备注的模糊搜索
- 🏷️ **标签筛选**: 多选标签筛选，支持标签统计
- ⚡ **紧迫度管理**: 高/中/低三级紧迫度分类
- ⭐ **重要度评级**: 1-5星重要度评级系统
- 📅 **提醒功能**: 支持日期提醒和筛选
- 📊 **数据可视化**: ECharts图表展示统计信息
- 🔄 **批量操作**: 批量删除、批量添加标签
- 📱 **响应式设计**: 适配桌面和移动设备

## 技术栈

- **前端框架**: Vue 2.6.14
- **HTTP客户端**: Axios 1.6.0
- **图表库**: ECharts 5.4.3
- **路由**: Vue Router 3.6.5 (可选)
- **状态管理**: Vuex 3.6.2 (可选)
- **样式框架**: Tailwind CSS
- **构建工具**: Vue CLI

## 项目结构

```
vue2-bookmark-manager/
├── public/
│   └── index.html              # HTML模板
├── src/
│   ├── components/             # Vue组件
│   │   ├── BookmarkCard.vue    # 书签卡片组件
│   │   ├── BookmarkForm.vue    # 书签表单组件
│   │   ├── FilterSidebar.vue   # 筛选侧边栏组件
│   │   └── StatsChart.vue      # 统计图表组件
│   ├── services/               # 服务层
│   │   └── api.js              # API服务
│   ├── utils/                  # 工具函数
│   │   ├── constants.js        # 常量定义
│   │   └── helpers.js          # 辅助函数
│   ├── assets/                 # 静态资源
│   │   └── styles.css          # 全局样式
│   ├── App.vue                 # 主应用组件
│   └── main.js                 # 应用入口
├── package.json                # 项目配置
└── README.md                   # 项目说明
```

## 快速开始

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0 或 yarn >= 1.0.0

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 环境配置

创建 `.env` 文件配置API地址：

```env
VUE_APP_API_BASE_URL=http://localhost:5000/api
```

### 开发模式

```bash
# 启动开发服务器
npm run serve

# 或
yarn serve
```

应用将在 `http://localhost:8080` 启动

### 生产构建

```bash
# 构建生产版本
npm run build

# 或
yarn build
```

构建文件将输出到 `dist/` 目录

## API集成

### 后端API要求

应用需要连接到兼容的后端API服务，API应提供以下端点：

- `GET /api/health` - 健康检查
- `GET /api/bookmarks` - 获取书签列表
- `POST /api/bookmarks` - 创建书签
- `GET /api/bookmarks/{id}` - 获取单个书签
- `PUT /api/bookmarks/{id}` - 更新书签
- `DELETE /api/bookmarks/{id}` - 删除书签
- `DELETE /api/bookmarks/batch` - 批量删除书签
- `POST /api/bookmarks/batch/tags` - 批量添加标签
- `GET /api/tags` - 获取标签统计
- `GET /api/stats` - 获取统计信息
- `GET /api/sync/export` - 导出数据
- `POST /api/sync/import` - 导入数据

### API响应格式

所有API响应应遵循统一格式：

```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述"
  }
}
```

## 组件说明

### BookmarkCard 书签卡片
- 展示书签基本信息
- 支持选择和高亮搜索关键词
- 显示标签、紧迫度、重要度等属性

### BookmarkForm 书签表单
- 新增/编辑书签表单
- 表单验证和错误处理
- 支持标签管理和星级评分

### FilterSidebar 筛选侧边栏
- 搜索功能
- 多维度筛选（标签、紧迫度、重要度、日期）
- 排序选项
- 活动筛选条件显示

### StatsChart 统计图表
- 使用ECharts展示数据统计
- 标签使用分布饼图
- 重要度分布柱状图
- 紧迫度统计

## 功能使用指南

### 书签管理
1. **新增书签**: 点击"新增书签"按钮，填写表单信息
2. **编辑书签**: 点击书签卡片，在右侧详情面板编辑
3. **删除书签**: 在书签详情面板点击删除按钮

### 搜索和筛选
1. **关键词搜索**: 在左侧搜索框输入关键词
2. **标签筛选**: 选择一个或多个标签进行筛选
3. **属性筛选**: 按紧迫度、重要度、提醒日期筛选
4. **排序**: 选择排序字段和排序方向

### 批量操作
1. **选择书签**: 勾选书签卡片右上角的复选框
2. **批量添加标签**: 选中书签后点击"批量添加标签"
3. **批量删除**: 选中书签后点击"批量删除"

### 数据同步
1. **导出数据**: 点击顶部"导出"按钮下载JSON文件
2. **云端同步**: 点击"同步数据"按钮进行上传/下载

## 自定义配置

### 修改API地址
在 `.env` 文件中修改 `VUE_APP_API_BASE_URL`

### 自定义样式
在 `src/assets/styles.css` 中添加自定义CSS

### 修改常量
在 `src/utils/constants.js` 中修改应用常量

## 浏览器支持

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 开发指南

### 代码规范
- 使用ESLint进行代码检查
- 遵循Vue官方风格指南
- 组件命名使用PascalCase
- 文件命名使用kebab-case

### 组件开发
1. 组件应该是可复用的
2. 使用props进行数据传递
3. 使用事件进行组件通信
4. 添加适当的注释和文档

### 样式开发
1. 优先使用Tailwind CSS类
2. 自定义样式使用scoped
3. 保持响应式设计
4. 注意无障碍访问

## 部署

### 静态部署
构建后将 `dist/` 目录部署到静态服务器

### Docker部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 环境变量
生产环境需要设置正确的API地址

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查API服务是否启动
   - 确认API地址配置正确
   - 检查CORS设置

2. **图表不显示**
   - 确认ECharts正确加载
   - 检查数据格式是否正确
   - 查看浏览器控制台错误

3. **样式问题**
   - 确认Tailwind CSS正确加载
   - 检查CSS类名是否正确
   - 验证响应式断点

### 调试技巧
- 使用Vue DevTools进行调试
- 检查浏览器控制台错误
- 使用网络面板检查API请求

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的书签管理功能
- 搜索和筛选功能
- 数据统计和可视化
- 响应式设计

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: [Email]

---

感谢使用Vue2书签管理器！

