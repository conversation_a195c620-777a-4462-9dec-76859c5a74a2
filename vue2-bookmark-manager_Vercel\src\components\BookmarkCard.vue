<template>
  <div 
    class="bookmark-card bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer border-l-4"
    :class="[
      `border-l-${urgencyConfig.color.replace('#', '')}`,
      { 'ring-2 ring-blue-500': selected }
    ]"
    @click="$emit('select', bookmark)"
  >
    <!-- 书签头部 -->
    <div class="flex items-start justify-between mb-2">
      <div class="flex-1 min-w-0">
        <h3 
          class="text-lg font-semibold text-gray-900 truncate"
          :title="bookmark.title"
          v-html="highlightedTitle"
        ></h3>
        <a 
          :href="bookmark.url" 
          target="_blank" 
          class="text-sm text-blue-600 hover:text-blue-800 truncate block mt-1"
          :title="bookmark.url"
          @click.stop
        >
          {{ bookmark.url }}
        </a>
      </div>
      
      <!-- 选择框 -->
      <div class="ml-2 flex-shrink-0">
        <input
          type="checkbox"
          :checked="selected"
          @change="$emit('toggle-select', bookmark)"
          @click.stop
          class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
      </div>
    </div>

    <!-- 标签 -->
    <div class="flex flex-wrap gap-1 mb-2" v-if="bookmark.tags && bookmark.tags.length > 0">
      <span
        v-for="tag in bookmark.tags"
        :key="tag"
        class="inline-block px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"
      >
        {{ tag }}
      </span>
    </div>

    <!-- 属性信息 -->
    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
      <!-- 紧迫度 -->
      <div class="flex items-center">
        <span 
          class="inline-block w-2 h-2 rounded-full mr-1"
          :style="{ backgroundColor: urgencyConfig.color }"
        ></span>
        <span>{{ urgencyConfig.label }}</span>
      </div>

      <!-- 重要度 -->
      <div class="flex items-center">
        <span class="text-yellow-500 mr-1">{{ stars }}</span>
        <span>({{ bookmark.importance }})</span>
      </div>

      <!-- 提醒日期 -->
      <div v-if="bookmark.reminder" class="flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <span>{{ formattedReminder }}</span>
      </div>
    </div>

    <!-- 备注 -->
    <div v-if="bookmark.comment" class="text-sm text-gray-700 bg-gray-50 rounded p-2">
      <p class="line-clamp-2" :title="bookmark.comment">{{ bookmark.comment }}</p>
    </div>

    <!-- 时间信息 -->
    <div class="flex justify-between text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
      <span>创建: {{ formatDate(bookmark.created_at) }}</span>
      <span v-if="bookmark.updated_at !== bookmark.created_at">
        更新: {{ formatDate(bookmark.updated_at) }}
      </span>
    </div>
  </div>
</template>

<script>
import { getUrgencyConfig, generateStars, formatDate, highlightKeyword } from '../utils/helpers'

export default {
  name: 'BookmarkCard',
  props: {
    bookmark: {
      type: Object,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    searchKeyword: {
      type: String,
      default: ''
    }
  },
  computed: {
    urgencyConfig() {
      return getUrgencyConfig(this.bookmark.urgency)
    },
    stars() {
      return generateStars(this.bookmark.importance)
    },
    formattedReminder() {
      return formatDate(this.bookmark.reminder)
    },
    highlightedTitle() {
      return highlightKeyword(this.bookmark.title, this.searchKeyword)
    }
  },
  methods: {
    formatDate
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bookmark-card:hover {
  transform: translateY(-1px);
}

/* 高亮样式 */
:deep(mark) {
  background-color: #fef08a;
  padding: 0 2px;
  border-radius: 2px;
}
</style>

