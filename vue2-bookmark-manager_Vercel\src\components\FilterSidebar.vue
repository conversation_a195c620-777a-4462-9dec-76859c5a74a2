<template>
  <div class="filter-sidebar bg-white rounded-lg shadow-md p-4">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-lg font-semibold text-gray-900">筛选条件</h2>
      <button
        @click="resetFilters"
        class="text-sm text-blue-600 hover:text-blue-800 transition-colors"
      >
        重置
      </button>
    </div>

    <!-- 搜索框 -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">搜索</label>
      <div class="relative">
        <input
          v-model="localFilters.search"
          type="text"
          placeholder="搜索标题、URL、备注..."
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          @input="debouncedSearch"
        />
        <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>
    </div>

    <!-- 标签筛选 -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">标签</label>
      <div class="max-h-40 overflow-y-auto">
        <div
          v-for="tag in availableTags"
          :key="tag.name"
          class="flex items-center justify-between py-1"
        >
          <label class="flex items-center cursor-pointer">
            <input
              type="checkbox"
              :value="tag.name"
              v-model="localFilters.tags"
              @change="updateFilters"
              class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">{{ tag.name }}</span>
          </label>
          <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            {{ tag.count }}
          </span>
        </div>
      </div>
      <div v-if="availableTags.length === 0" class="text-sm text-gray-500 py-2">
        暂无标签
      </div>
    </div>

    <!-- 紧迫度筛选 -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">紧迫度</label>
      <div class="space-y-2">
        <label
          v-for="option in urgencyOptions"
          :key="option.value"
          class="flex items-center cursor-pointer"
        >
          <input
            type="checkbox"
            :value="option.value"
            v-model="localFilters.urgency"
            @change="updateFilters"
            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
          <span
            class="ml-2 inline-block w-2 h-2 rounded-full"
            :style="{ backgroundColor: option.color }"
          ></span>
          <span class="ml-2 text-sm text-gray-700">{{ option.label }}</span>
        </label>
      </div>
    </div>

    <!-- 重要度筛选 -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">重要度</label>
      <div class="space-y-2">
        <label
          v-for="option in importanceOptions"
          :key="option.value"
          class="flex items-center cursor-pointer"
        >
          <input
            type="checkbox"
            :value="option.value"
            v-model="localFilters.importance"
            @change="updateFilters"
            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
          <span class="ml-2 text-sm text-gray-700">{{ option.label }}</span>
          <span class="ml-1 text-yellow-500">{{ '★'.repeat(option.value) }}</span>
        </label>
      </div>
    </div>

    <!-- 提醒日期筛选 -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">提醒日期</label>
      <div class="space-y-2">
        <label
          v-for="option in reminderOptions"
          :key="option.value"
          class="flex items-center cursor-pointer"
        >
          <input
            type="radio"
            :value="option.value"
            v-model="localFilters.reminderFilter"
            @change="updateReminderFilter"
            class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
          />
          <span class="ml-2 text-sm text-gray-700">{{ option.label }}</span>
        </label>
      </div>
      
      <!-- 自定义日期范围 -->
      <div v-if="localFilters.reminderFilter === 'custom'" class="mt-2 space-y-2">
        <input
          v-model="localFilters.reminderStart"
          type="date"
          placeholder="开始日期"
          @change="updateFilters"
          class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <input
          v-model="localFilters.reminderEnd"
          type="date"
          placeholder="结束日期"
          @change="updateFilters"
          class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>

    <!-- 排序选项 -->
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">排序</label>
      <div class="grid grid-cols-2 gap-2">
        <select
          v-model="localFilters.sort"
          @change="updateFilters"
          class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option
            v-for="option in sortOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>
        <select
          v-model="localFilters.order"
          @change="updateFilters"
          class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option
            v-for="option in orderOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>
      </div>
    </div>

    <!-- 活动筛选条件显示 -->
    <div v-if="hasActiveFilters" class="border-t border-gray-200 pt-4">
      <h3 class="text-sm font-medium text-gray-700 mb-2">活动筛选</h3>
      <div class="flex flex-wrap gap-1">
        <!-- 搜索关键词 -->
        <span
          v-if="localFilters.search"
          class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"
        >
          搜索: {{ localFilters.search }}
          <button @click="clearSearch" class="ml-1 text-blue-600 hover:text-blue-800">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </span>
        
        <!-- 选中的标签 -->
        <span
          v-for="tag in localFilters.tags"
          :key="`tag-${tag}`"
          class="inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full"
        >
          {{ tag }}
          <button @click="removeTag(tag)" class="ml-1 text-green-600 hover:text-green-800">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  URGENCY_OPTIONS, 
  IMPORTANCE_OPTIONS, 
  SORT_OPTIONS, 
  ORDER_OPTIONS, 
  REMINDER_FILTER_OPTIONS 
} from '../utils/constants'
import { debounce } from '../utils/helpers'

export default {
  name: 'FilterSidebar',
  props: {
    filters: {
      type: Object,
      required: true
    },
    availableTags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localFilters: {
        search: '',
        tags: [],
        urgency: [],
        importance: [],
        reminderFilter: '',
        reminderStart: '',
        reminderEnd: '',
        sort: 'created_at',
        order: 'desc'
      },
      urgencyOptions: URGENCY_OPTIONS,
      importanceOptions: IMPORTANCE_OPTIONS,
      sortOptions: SORT_OPTIONS,
      orderOptions: ORDER_OPTIONS,
      reminderOptions: REMINDER_FILTER_OPTIONS
    }
  },
  computed: {
    hasActiveFilters() {
      return (
        this.localFilters.search ||
        this.localFilters.tags.length > 0 ||
        this.localFilters.urgency.length > 0 ||
        this.localFilters.importance.length > 0 ||
        this.localFilters.reminderFilter
      )
    }
  },
  watch: {
    filters: {
      handler(newFilters) {
        this.localFilters = { ...this.localFilters, ...newFilters }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // 创建防抖搜索函数
    this.debouncedSearch = debounce(() => {
      this.updateFilters()
    }, 300)
  },
  methods: {
    updateFilters() {
      this.$emit('update-filters', { ...this.localFilters })
    },
    
    updateReminderFilter() {
      // 根据选择的提醒筛选类型设置日期范围
      const today = new Date()
      const formatDate = (date) => date.toISOString().split('T')[0]
      
      switch (this.localFilters.reminderFilter) {
        case 'today':
          this.localFilters.reminderStart = formatDate(today)
          this.localFilters.reminderEnd = formatDate(today)
          break
        case 'week':
          const weekStart = new Date(today)
          weekStart.setDate(today.getDate() - today.getDay())
          const weekEnd = new Date(weekStart)
          weekEnd.setDate(weekStart.getDate() + 6)
          this.localFilters.reminderStart = formatDate(weekStart)
          this.localFilters.reminderEnd = formatDate(weekEnd)
          break
        case 'month':
          const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
          const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0)
          this.localFilters.reminderStart = formatDate(monthStart)
          this.localFilters.reminderEnd = formatDate(monthEnd)
          break
        case 'custom':
          // 自定义日期范围，不自动设置
          break
        default:
          this.localFilters.reminderStart = ''
          this.localFilters.reminderEnd = ''
      }
      
      this.updateFilters()
    },
    
    resetFilters() {
      this.localFilters = {
        search: '',
        tags: [],
        urgency: [],
        importance: [],
        reminderFilter: '',
        reminderStart: '',
        reminderEnd: '',
        sort: 'created_at',
        order: 'desc'
      }
      this.updateFilters()
    },
    
    clearSearch() {
      this.localFilters.search = ''
      this.updateFilters()
    },
    
    removeTag(tag) {
      const index = this.localFilters.tags.indexOf(tag)
      if (index > -1) {
        this.localFilters.tags.splice(index, 1)
        this.updateFilters()
      }
    }
  }
}
</script>

<style scoped>
.filter-sidebar {
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
}

/* 自定义滚动条 */
.filter-sidebar::-webkit-scrollbar {
  width: 4px;
}

.filter-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.filter-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.filter-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>

