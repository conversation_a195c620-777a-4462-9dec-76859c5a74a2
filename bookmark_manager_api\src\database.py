from pymongo import MongoClient
from pymongo.errors import ConnectionFailure
import os
from datetime import datetime

# Load environment variables from .env file for local development
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv not installed, skip loading .env file
    pass

class Database:
    def __init__(self):
        self.client = None
        self.db = None
        
    def connect(self, connection_string, database_name="bookmark_manager"):
        """连接到MongoDB数据库"""
        try:
            self.client = MongoClient(connection_string)
            # 测试连接
            self.client.admin.command('ping')
            self.db = self.client[database_name]
            print(f"成功连接到MongoDB数据库: {database_name}")
            return True
        except ConnectionFailure as e:
            print(f"MongoDB连接失败: {e}")
            return False
        except Exception as e:
            print(f"数据库连接错误: {e}")
            return False
    
    def get_collection(self, collection_name):
        """获取集合"""
        if self.db is None:
            raise Exception("数据库未连接")
        return self.db[collection_name]
    
    def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()

# 全局数据库实例
db_instance = Database()

def init_database(app):
    """初始化数据库连接"""
    # Use environment variable for connection string in production
    # For Railway deployment, set MONGODB_URI in Railway's environment variables
    connection_string = os.environ.get(
        'MONGODB_URI',
        "***********************************************************************"
    )
    # Use environment variable for database name
    # For Railway deployment, set MONGODB_DATABASE=Honor in Railway's environment variables
    database_name = os.environ.get('MONGODB_DATABASE', "Honor")

    print(f"正在连接到数据库: {database_name}")
    print(f"连接字符串: {connection_string[:20]}...")  # Only show first 20 chars for security

    success = db_instance.connect(connection_string, database_name=database_name)

    if success:
        # 创建索引以提高查询性能 - using CataPage collection
        catapage_collection = db_instance.get_collection('CataPage')

        # 为常用查询字段创建索引
        catapage_collection.create_index("title")
        catapage_collection.create_index("tags")
        catapage_collection.create_index("urgency")
        catapage_collection.create_index("importance")
        catapage_collection.create_index("timestamp")  # Using timestamp instead of created_at

        print(f"数据库索引创建完成 - {database_name}数据库CataPage集合")
    else:
        print(f"数据库连接失败 - {database_name}")

    return success

