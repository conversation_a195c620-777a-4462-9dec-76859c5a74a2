(function(){"use strict";var t={366:function(t,e,a){var s=a(471),r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"min-h-screen bg-gray-100",attrs:{id:"app"}},[e("header",{staticClass:"bg-white shadow-sm border-b border-gray-200"},[e("div",{staticClass:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},[e("div",{staticClass:"flex justify-between items-center h-16"},[t._m(0),e("div",{staticClass:"flex items-center space-x-4"},[e("button",{staticClass:"flex items-center px-3 py-2 text-sm text-gray-700 hover:text-gray-900 transition-colors",on:{click:function(e){t.showSyncDialog=!0}}},[e("svg",{staticClass:"w-4 h-4 mr-1",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"}})]),t._v(" 同步数据 ")]),e("button",{staticClass:"flex items-center px-3 py-2 text-sm text-gray-700 hover:text-gray-900 transition-colors",on:{click:t.exportData}},[e("svg",{staticClass:"w-4 h-4 mr-1",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"}})]),t._v(" 导出 ")]),e("div",{staticClass:"flex items-center"},[e("div",{staticClass:"w-2 h-2 rounded-full mr-2",class:t.apiHealthy?"bg-green-500":"bg-red-500"}),e("span",{staticClass:"text-sm text-gray-600"},[t._v(" "+t._s(t.apiHealthy?"已连接":"连接失败")+" ")])])])])])]),e("main",{staticClass:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},[e("div",{staticClass:"grid grid-cols-12 gap-6"},[e("div",{staticClass:"col-span-3"},[e("FilterSidebar",{attrs:{filters:t.filters,"available-tags":t.availableTags},on:{"update-filters":t.handleFiltersUpdate}})],1),e("div",{staticClass:"col-span-6"},[e("div",{staticClass:"bg-white rounded-lg shadow-md p-4 mb-6"},[e("div",{staticClass:"flex items-center justify-between"},[e("div",{staticClass:"flex items-center space-x-4"},[e("button",{staticClass:"flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors",on:{click:function(e){t.showBookmarkForm=!0}}},[e("svg",{staticClass:"w-4 h-4 mr-2",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"}})]),t._v(" 新增书签 ")]),t.selectedBookmarks.length>0?e("button",{staticClass:"flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors",on:{click:function(e){t.showBatchTagDialog=!0}}},[e("svg",{staticClass:"w-4 h-4 mr-2",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a2 2 0 012-2z"}})]),t._v(" 批量添加标签 ("+t._s(t.selectedBookmarks.length)+") ")]):t._e(),t.selectedBookmarks.length>0?e("button",{staticClass:"flex items-center px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors",on:{click:t.batchDeleteBookmarks}},[e("svg",{staticClass:"w-4 h-4 mr-2",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"}})]),t._v(" 批量删除 ("+t._s(t.selectedBookmarks.length)+") ")]):t._e()]),e("div",{staticClass:"text-sm text-gray-600"},[t._v(" 共 "+t._s(t.pagination.total)+" 个书签 ")])])]),e("div",{staticClass:"space-y-4"},t._l(t.bookmarks,function(a){return e("BookmarkCard",{key:a.id,attrs:{bookmark:a,selected:t.selectedBookmarks.includes(a.id),"search-keyword":t.filters.search},on:{select:t.selectBookmark,"toggle-select":t.toggleSelectBookmark}})}),1),t.loading?e("div",{staticClass:"text-center py-8"},[e("div",{staticClass:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),e("p",{staticClass:"mt-2 text-gray-600"},[t._v("加载中...")])]):t._e(),t.loading||0!==t.bookmarks.length?t._e():e("div",{staticClass:"text-center py-12"},[e("svg",{staticClass:"mx-auto h-12 w-12 text-gray-400",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"}})]),e("h3",{staticClass:"mt-2 text-sm font-medium text-gray-900"},[t._v("暂无书签")]),e("p",{staticClass:"mt-1 text-sm text-gray-500"},[t._v("开始添加您的第一个书签吧")]),e("div",{staticClass:"mt-6"},[e("button",{staticClass:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",on:{click:function(e){t.showBookmarkForm=!0}}},[e("svg",{staticClass:"w-4 h-4 mr-2",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"}})]),t._v(" 新增书签 ")])])]),t.pagination.pages>1?e("div",{staticClass:"mt-6 flex justify-center"},[e("nav",{staticClass:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},[e("button",{staticClass:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",attrs:{disabled:t.pagination.page<=1},on:{click:function(e){return t.changePage(t.pagination.page-1)}}},[t._v(" 上一页 ")]),t._l(t.visiblePages,function(a){return e("button",{key:a,class:["relative inline-flex items-center px-4 py-2 border text-sm font-medium",a===t.pagination.page?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"],on:{click:function(e){return t.changePage(a)}}},[t._v(" "+t._s(a)+" ")])}),e("button",{staticClass:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",attrs:{disabled:t.pagination.page>=t.pagination.pages},on:{click:function(e){return t.changePage(t.pagination.page+1)}}},[t._v(" 下一页 ")])],2)]):t._e()]),e("div",{staticClass:"col-span-3"},[t.showBookmarkForm||t.selectedBookmark?e("div",[e("BookmarkForm",{attrs:{bookmark:t.selectedBookmark,loading:t.formLoading},on:{submit:t.handleBookmarkSubmit,delete:t.handleBookmarkDelete,close:t.closeBookmarkForm}})],1):e("div",[e("StatsChart",{attrs:{stats:t.stats,"tag-stats":t.availableTags}})],1)])])]),t.showSyncDialog?e("div",{staticClass:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},[e("div",{staticClass:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},[e("div",{staticClass:"mt-3"},[e("h3",{staticClass:"text-lg font-medium text-gray-900 mb-4"},[t._v("数据同步")]),e("div",{staticClass:"space-y-3"},[e("button",{staticClass:"w-full flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors",on:{click:t.syncToCloud}},[e("svg",{staticClass:"w-4 h-4 mr-2",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"}})]),t._v(" 上传到云端 ")]),e("button",{staticClass:"w-full flex items-center justify-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors",on:{click:t.syncFromCloud}},[e("svg",{staticClass:"w-4 h-4 mr-2",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"}})]),t._v(" 从云端下载 ")])]),e("div",{staticClass:"mt-4 flex justify-end"},[e("button",{staticClass:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",on:{click:function(e){t.showSyncDialog=!1}}},[t._v(" 取消 ")])])])])]):t._e(),t.showBatchTagDialog?e("div",{staticClass:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},[e("div",{staticClass:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},[e("div",{staticClass:"mt-3"},[e("h3",{staticClass:"text-lg font-medium text-gray-900 mb-4"},[t._v("批量添加标签")]),e("p",{staticClass:"text-sm text-gray-600 mb-4"},[t._v("为选中的 "+t._s(t.selectedBookmarks.length)+" 个书签添加标签")]),e("div",{staticClass:"mb-4"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.batchTagInput,expression:"batchTagInput"}],staticClass:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",attrs:{type:"text",placeholder:"输入标签，用逗号分隔"},domProps:{value:t.batchTagInput},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleBatchAddTags.apply(null,arguments)},input:function(e){e.target.composing||(t.batchTagInput=e.target.value)}}})]),e("div",{staticClass:"flex justify-end space-x-2"},[e("button",{staticClass:"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors",on:{click:function(e){t.showBatchTagDialog=!1}}},[t._v(" 取消 ")]),e("button",{staticClass:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors",on:{click:t.handleBatchAddTags}},[t._v(" 添加 ")])])])])]):t._e(),t.message.show?e("div",{staticClass:"fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"},[e("div",{staticClass:"p-4"},[e("div",{staticClass:"flex items-start"},[e("div",{staticClass:"flex-shrink-0"},["success"===t.message.type?e("svg",{staticClass:"h-6 w-6 text-green-400",attrs:{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"}})]):e("svg",{staticClass:"h-6 w-6 text-red-400",attrs:{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"}})])]),e("div",{staticClass:"ml-3 w-0 flex-1 pt-0.5"},[e("p",{staticClass:"text-sm font-medium text-gray-900"},[t._v(t._s(t.message.text))])]),e("div",{staticClass:"ml-4 flex-shrink-0 flex"},[e("button",{staticClass:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500",on:{click:t.hideMessage}},[e("svg",{staticClass:"h-5 w-5",attrs:{viewBox:"0 0 20 20",fill:"currentColor"}},[e("path",{attrs:{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"}})])])])])])]):t._e()])},o=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex items-center"},[e("h1",{staticClass:"text-xl font-semibold text-gray-900"},[t._v("书签管理器")]),e("span",{staticClass:"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"},[t._v(" Vue2 ")])])}],i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"bookmark-card bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer border-l-4",class:[`border-l-${t.urgencyConfig.color.replace("#","")}`,{"ring-2 ring-blue-500":t.selected}],on:{click:function(e){return t.$emit("select",t.bookmark)}}},[e("div",{staticClass:"flex items-start justify-between mb-2"},[e("div",{staticClass:"flex-1 min-w-0"},[e("h3",{staticClass:"text-lg font-semibold text-gray-900 truncate",attrs:{title:t.bookmark.title},domProps:{innerHTML:t._s(t.highlightedTitle)}}),e("a",{staticClass:"text-sm text-blue-600 hover:text-blue-800 truncate block mt-1",attrs:{href:t.bookmark.url,target:"_blank",title:t.bookmark.url},on:{click:function(t){t.stopPropagation()}}},[t._v(" "+t._s(t.bookmark.url)+" ")])]),e("div",{staticClass:"ml-2 flex-shrink-0"},[e("input",{staticClass:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",attrs:{type:"checkbox"},domProps:{checked:t.selected},on:{change:function(e){return t.$emit("toggle-select",t.bookmark)},click:function(t){t.stopPropagation()}}})])]),t.bookmark.tags&&t.bookmark.tags.length>0?e("div",{staticClass:"flex flex-wrap gap-1 mb-2"},t._l(t.bookmark.tags,function(a){return e("span",{key:a,staticClass:"inline-block px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"},[t._v(" "+t._s(a)+" ")])}),0):t._e(),e("div",{staticClass:"flex items-center justify-between text-sm text-gray-600 mb-2"},[e("div",{staticClass:"flex items-center"},[e("span",{staticClass:"inline-block w-2 h-2 rounded-full mr-1",style:{backgroundColor:t.urgencyConfig.color}}),e("span",[t._v(t._s(t.urgencyConfig.label))])]),e("div",{staticClass:"flex items-center"},[e("span",{staticClass:"text-yellow-500 mr-1"},[t._v(t._s(t.stars))]),e("span",[t._v("("+t._s(t.bookmark.importance)+")")])]),t.bookmark.reminder?e("div",{staticClass:"flex items-center"},[e("svg",{staticClass:"w-4 h-4 mr-1",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"}})]),e("span",[t._v(t._s(t.formattedReminder))])]):t._e()]),t.bookmark.comment?e("div",{staticClass:"text-sm text-gray-700 bg-gray-50 rounded p-2"},[e("p",{staticClass:"line-clamp-2",attrs:{title:t.bookmark.comment}},[t._v(t._s(t.bookmark.comment))])]):t._e(),e("div",{staticClass:"flex justify-between text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100"},[e("span",[t._v("创建: "+t._s(t.formatDate(t.bookmark.created_at)))]),t.bookmark.updated_at!==t.bookmark.created_at?e("span",[t._v(" 更新: "+t._s(t.formatDate(t.bookmark.updated_at))+" ")]):t._e()])])},l=[];const n=[{value:"high",label:"高",color:"#ef4444",bgColor:"#fef2f2"},{value:"medium",label:"中",color:"#f59e0b",bgColor:"#fffbeb"},{value:"low",label:"低",color:"#10b981",bgColor:"#f0fdf4"}],c=[{value:1,label:"1星"},{value:2,label:"2星"},{value:3,label:"3星"},{value:4,label:"4星"},{value:5,label:"5星"}],d=[{value:"created_at",label:"创建时间"},{value:"updated_at",label:"更新时间"},{value:"title",label:"标题"},{value:"importance",label:"重要度"},{value:"urgency",label:"紧迫度"}],u=[{value:"desc",label:"降序"},{value:"asc",label:"升序"}],m={DEFAULT_PAGE:1,DEFAULT_LIMIT:20,LIMIT_OPTIONS:[10,20,50,100]},g=[{value:"today",label:"今天"},{value:"week",label:"本周"},{value:"month",label:"本月"},{value:"custom",label:"自定义"}],h={title:"",url:"",tags:[],urgency:"low",importance:1,reminder:null,comment:""},p={title:{required:!0,message:"标题不能为空"},url:{required:!0,pattern:/^https?:\/\/.+/,message:"请输入有效的URL地址"}},b={NETWORK_ERROR:"网络连接失败，请检查网络设置",SERVER_ERROR:"服务器错误，请稍后重试",VALIDATION_ERROR:"数据验证失败",NOT_FOUND:"请求的资源不存在",UNKNOWN_ERROR:"未知错误，请联系管理员"},f={CREATE_SUCCESS:"书签创建成功",UPDATE_SUCCESS:"书签更新成功",DELETE_SUCCESS:"书签删除成功",BATCH_DELETE_SUCCESS:"批量删除成功",BATCH_ADD_TAGS_SUCCESS:"批量添加标签成功",EXPORT_SUCCESS:"数据导出成功",IMPORT_SUCCESS:"数据导入成功"};function v(t,e="date"){if(!t)return"";const a=new Date(t);if(isNaN(a.getTime()))return"";const s=a.getFullYear(),r=String(a.getMonth()+1).padStart(2,"0"),o=String(a.getDate()).padStart(2,"0"),i=String(a.getHours()).padStart(2,"0"),l=String(a.getMinutes()).padStart(2,"0");switch(e){case"datetime":return`${s}-${r}-${o} ${i}:${l}`;case"time":return`${i}:${l}`;case"date":default:return`${s}-${r}-${o}`}}function k(t){return n.find(e=>e.value===t)||n[2]}function y(t){const e="★".repeat(t),a="☆".repeat(5-t);return e+a}function x(t){try{return new URL(t),!0}catch{return!1}}function C(t,e){let a;return function(...s){clearTimeout(a),a=setTimeout(()=>t.apply(this,s),e)}}function w(t){if(null===t||"object"!==typeof t)return t;if(t instanceof Date)return new Date(t.getTime());if(t instanceof Array)return t.map(t=>w(t));if("object"===typeof t){const e={};for(const a in t)t.hasOwnProperty(a)&&(e[a]=w(t[a]));return e}}function _(t){if(!t)return b.UNKNOWN_ERROR;if(!t.status)return b.NETWORK_ERROR;switch(t.status){case 400:return t.message||b.VALIDATION_ERROR;case 404:return b.NOT_FOUND;case 500:return b.SERVER_ERROR;default:return t.message||b.UNKNOWN_ERROR}}function F(t){return null===t||void 0===t||("string"===typeof t?""===t.trim():Array.isArray(t)?0===t.length:"object"===typeof t&&0===Object.keys(t).length)}function D(t,e){if(!e||!t)return t;const a=new RegExp(`(${e})`,"gi");return t.replace(a,"<mark>$1</mark>")}var S={name:"BookmarkCard",props:{bookmark:{type:Object,required:!0},selected:{type:Boolean,default:!1},searchKeyword:{type:String,default:""}},computed:{urgencyConfig(){return k(this.bookmark.urgency)},stars(){return y(this.bookmark.importance)},formattedReminder(){return v(this.bookmark.reminder)},highlightedTitle(){return D(this.bookmark.title,this.searchKeyword)}},methods:{formatDate:v}},T=S,B=a(656),E=(0,B.A)(T,i,l,!1,null,"73450c1e",null),A=E.exports,O=function(){var t=this,e=t._self._c;return e("div",{staticClass:"bookmark-form bg-white rounded-lg shadow-md p-6"},[e("div",{staticClass:"flex items-center justify-between mb-4"},[e("h2",{staticClass:"text-xl font-semibold text-gray-900"},[t._v(" "+t._s(t.isEditing?"编辑书签":"新增书签")+" ")]),e("button",{staticClass:"text-gray-400 hover:text-gray-600 transition-colors",on:{click:function(e){return t.$emit("close")}}},[e("svg",{staticClass:"w-6 h-6",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"}})])])]),e("form",{on:{submit:function(e){return e.preventDefault(),t.handleSubmit.apply(null,arguments)}}},[e("div",{staticClass:"mb-4"},[t._m(0),e("input",{directives:[{name:"model",rawName:"v-model",value:t.formData.title,expression:"formData.title"}],staticClass:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",class:{"border-red-500":t.errors.title},attrs:{type:"text",placeholder:"请输入书签标题"},domProps:{value:t.formData.title},on:{input:function(e){e.target.composing||t.$set(t.formData,"title",e.target.value)}}}),t.errors.title?e("p",{staticClass:"text-red-500 text-sm mt-1"},[t._v(t._s(t.errors.title))]):t._e()]),e("div",{staticClass:"mb-4"},[t._m(1),e("input",{directives:[{name:"model",rawName:"v-model",value:t.formData.url,expression:"formData.url"}],staticClass:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",class:{"border-red-500":t.errors.url},attrs:{type:"url",placeholder:"https://example.com"},domProps:{value:t.formData.url},on:{input:function(e){e.target.composing||t.$set(t.formData,"url",e.target.value)}}}),t.errors.url?e("p",{staticClass:"text-red-500 text-sm mt-1"},[t._v(t._s(t.errors.url))]):t._e()]),e("div",{staticClass:"mb-4"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("标签")]),e("div",{staticClass:"flex flex-wrap gap-2 mb-2"},t._l(t.formData.tags,function(a,s){return e("span",{key:s,staticClass:"inline-flex items-center px-2 py-1 text-sm bg-blue-100 text-blue-800 rounded-full"},[t._v(" "+t._s(a)+" "),e("button",{staticClass:"ml-1 text-blue-600 hover:text-blue-800",attrs:{type:"button"},on:{click:function(e){return t.removeTag(s)}}},[e("svg",{staticClass:"w-3 h-3",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"}})])])])}),0),e("div",{staticClass:"flex"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.newTag,expression:"newTag"}],staticClass:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",attrs:{type:"text",placeholder:"输入标签后按回车添加"},domProps:{value:t.newTag},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.addTag.apply(null,arguments)},input:function(e){e.target.composing||(t.newTag=e.target.value)}}}),e("button",{staticClass:"px-4 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 transition-colors",attrs:{type:"button"},on:{click:t.addTag}},[t._v(" 添加 ")])])]),e("div",{staticClass:"grid grid-cols-2 gap-4 mb-4"},[e("div",[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("紧迫度")]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.formData.urgency,expression:"formData.urgency"}],staticClass:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",on:{change:function(e){var a=Array.prototype.filter.call(e.target.options,function(t){return t.selected}).map(function(t){var e="_value"in t?t._value:t.value;return e});t.$set(t.formData,"urgency",e.target.multiple?a:a[0])}}},t._l(t.urgencyOptions,function(a){return e("option",{key:a.value,domProps:{value:a.value}},[t._v(" "+t._s(a.label)+" ")])}),0)]),e("div",[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("重要度")]),e("div",{staticClass:"flex items-center space-x-1"},[t._l(5,function(a){return e("button",{key:a,staticClass:"text-2xl transition-colors",class:a<=t.formData.importance?"text-yellow-500":"text-gray-300",attrs:{type:"button"},on:{click:function(e){t.formData.importance=a}}},[t._v(" ★ ")])}),e("span",{staticClass:"ml-2 text-sm text-gray-600"},[t._v("("+t._s(t.formData.importance)+"星)")])],2)])]),e("div",{staticClass:"mb-4"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("提醒日期")]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.formData.reminder,expression:"formData.reminder"}],staticClass:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",attrs:{type:"date"},domProps:{value:t.formData.reminder},on:{input:function(e){e.target.composing||t.$set(t.formData,"reminder",e.target.value)}}})]),e("div",{staticClass:"mb-6"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("备注")]),e("textarea",{directives:[{name:"model",rawName:"v-model",value:t.formData.comment,expression:"formData.comment"}],staticClass:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",attrs:{rows:"3",placeholder:"请输入备注信息"},domProps:{value:t.formData.comment},on:{input:function(e){e.target.composing||t.$set(t.formData,"comment",e.target.value)}}})]),e("div",{staticClass:"flex justify-between"},[e("div",[t.isEditing?e("button",{staticClass:"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors",attrs:{type:"button",disabled:t.loading},on:{click:t.handleDelete}},[t._v(" 删除 ")]):t._e()]),e("div",{staticClass:"flex space-x-2"},[e("button",{staticClass:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors",attrs:{type:"button"},on:{click:function(e){return t.$emit("close")}}},[t._v(" 取消 ")]),e("button",{staticClass:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50",attrs:{type:"submit",disabled:t.loading}},[t._v(" "+t._s(t.loading?"保存中...":t.isEditing?"更新":"创建")+" ")])])])])])},R=[function(){var t=this,e=t._self._c;return e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v(" 标题 "),e("span",{staticClass:"text-red-500"},[t._v("*")])])},function(){var t=this,e=t._self._c;return e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v(" URL "),e("span",{staticClass:"text-red-500"},[t._v("*")])])}],j={name:"BookmarkForm",props:{bookmark:{type:Object,default:null},loading:{type:Boolean,default:!1}},data(){return{formData:w(h),newTag:"",errors:{},urgencyOptions:n}},computed:{isEditing(){return!!this.bookmark}},watch:{bookmark:{handler(t){t?(this.formData=w(t),Array.isArray(this.formData.tags)||(this.formData.tags=[])):this.formData=w(h),this.errors={}},immediate:!0}},methods:{addTag(){const t=this.newTag.trim();t&&!this.formData.tags.includes(t)&&(this.formData.tags.push(t),this.newTag="")},removeTag(t){this.formData.tags.splice(t,1)},validateForm(){return this.errors={},F(this.formData.title)&&(this.errors.title=p.title.message),F(this.formData.url)?this.errors.url=p.url.message:x(this.formData.url)||(this.errors.url=p.url.message),0===Object.keys(this.errors).length},handleSubmit(){if(!this.validateForm())return;const t=w(this.formData);""===t.reminder&&(t.reminder=null),this.$emit("submit",t)},handleDelete(){confirm("确定要删除这个书签吗？")&&this.$emit("delete",this.bookmark.id)}}},M=j,$=(0,B.A)(M,O,R,!1,null,"730f3b61",null),L=$.exports,P=function(){var t=this,e=t._self._c;return e("div",{staticClass:"filter-sidebar bg-white rounded-lg shadow-md p-4"},[e("div",{staticClass:"flex items-center justify-between mb-4"},[e("h2",{staticClass:"text-lg font-semibold text-gray-900"},[t._v("筛选条件")]),e("button",{staticClass:"text-sm text-blue-600 hover:text-blue-800 transition-colors",on:{click:t.resetFilters}},[t._v(" 重置 ")])]),e("div",{staticClass:"mb-6"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("搜索")]),e("div",{staticClass:"relative"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.search,expression:"localFilters.search"}],staticClass:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",attrs:{type:"text",placeholder:"搜索标题、URL、备注..."},domProps:{value:t.localFilters.search},on:{input:[function(e){e.target.composing||t.$set(t.localFilters,"search",e.target.value)},t.debouncedSearch]}}),e("svg",{staticClass:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"}})])])]),e("div",{staticClass:"mb-6"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("标签")]),e("div",{staticClass:"max-h-40 overflow-y-auto"},t._l(t.availableTags,function(a){return e("div",{key:a.name,staticClass:"flex items-center justify-between py-1"},[e("label",{staticClass:"flex items-center cursor-pointer"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.tags,expression:"localFilters.tags"}],staticClass:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",attrs:{type:"checkbox"},domProps:{value:a.name,checked:Array.isArray(t.localFilters.tags)?t._i(t.localFilters.tags,a.name)>-1:t.localFilters.tags},on:{change:[function(e){var s=t.localFilters.tags,r=e.target,o=!!r.checked;if(Array.isArray(s)){var i=a.name,l=t._i(s,i);r.checked?l<0&&t.$set(t.localFilters,"tags",s.concat([i])):l>-1&&t.$set(t.localFilters,"tags",s.slice(0,l).concat(s.slice(l+1)))}else t.$set(t.localFilters,"tags",o)},t.updateFilters]}}),e("span",{staticClass:"ml-2 text-sm text-gray-700"},[t._v(t._s(a.name))])]),e("span",{staticClass:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full"},[t._v(" "+t._s(a.count)+" ")])])}),0),0===t.availableTags.length?e("div",{staticClass:"text-sm text-gray-500 py-2"},[t._v(" 暂无标签 ")]):t._e()]),e("div",{staticClass:"mb-6"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("紧迫度")]),e("div",{staticClass:"space-y-2"},t._l(t.urgencyOptions,function(a){return e("label",{key:a.value,staticClass:"flex items-center cursor-pointer"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.urgency,expression:"localFilters.urgency"}],staticClass:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",attrs:{type:"checkbox"},domProps:{value:a.value,checked:Array.isArray(t.localFilters.urgency)?t._i(t.localFilters.urgency,a.value)>-1:t.localFilters.urgency},on:{change:[function(e){var s=t.localFilters.urgency,r=e.target,o=!!r.checked;if(Array.isArray(s)){var i=a.value,l=t._i(s,i);r.checked?l<0&&t.$set(t.localFilters,"urgency",s.concat([i])):l>-1&&t.$set(t.localFilters,"urgency",s.slice(0,l).concat(s.slice(l+1)))}else t.$set(t.localFilters,"urgency",o)},t.updateFilters]}}),e("span",{staticClass:"ml-2 inline-block w-2 h-2 rounded-full",style:{backgroundColor:a.color}}),e("span",{staticClass:"ml-2 text-sm text-gray-700"},[t._v(t._s(a.label))])])}),0)]),e("div",{staticClass:"mb-6"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("重要度")]),e("div",{staticClass:"space-y-2"},t._l(t.importanceOptions,function(a){return e("label",{key:a.value,staticClass:"flex items-center cursor-pointer"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.importance,expression:"localFilters.importance"}],staticClass:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",attrs:{type:"checkbox"},domProps:{value:a.value,checked:Array.isArray(t.localFilters.importance)?t._i(t.localFilters.importance,a.value)>-1:t.localFilters.importance},on:{change:[function(e){var s=t.localFilters.importance,r=e.target,o=!!r.checked;if(Array.isArray(s)){var i=a.value,l=t._i(s,i);r.checked?l<0&&t.$set(t.localFilters,"importance",s.concat([i])):l>-1&&t.$set(t.localFilters,"importance",s.slice(0,l).concat(s.slice(l+1)))}else t.$set(t.localFilters,"importance",o)},t.updateFilters]}}),e("span",{staticClass:"ml-2 text-sm text-gray-700"},[t._v(t._s(a.label))]),e("span",{staticClass:"ml-1 text-yellow-500"},[t._v(t._s("★".repeat(a.value)))])])}),0)]),e("div",{staticClass:"mb-6"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("提醒日期")]),e("div",{staticClass:"space-y-2"},t._l(t.reminderOptions,function(a){return e("label",{key:a.value,staticClass:"flex items-center cursor-pointer"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.reminderFilter,expression:"localFilters.reminderFilter"}],staticClass:"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500",attrs:{type:"radio"},domProps:{value:a.value,checked:t._q(t.localFilters.reminderFilter,a.value)},on:{change:[function(e){return t.$set(t.localFilters,"reminderFilter",a.value)},t.updateReminderFilter]}}),e("span",{staticClass:"ml-2 text-sm text-gray-700"},[t._v(t._s(a.label))])])}),0),"custom"===t.localFilters.reminderFilter?e("div",{staticClass:"mt-2 space-y-2"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.reminderStart,expression:"localFilters.reminderStart"}],staticClass:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",attrs:{type:"date",placeholder:"开始日期"},domProps:{value:t.localFilters.reminderStart},on:{change:t.updateFilters,input:function(e){e.target.composing||t.$set(t.localFilters,"reminderStart",e.target.value)}}}),e("input",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.reminderEnd,expression:"localFilters.reminderEnd"}],staticClass:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",attrs:{type:"date",placeholder:"结束日期"},domProps:{value:t.localFilters.reminderEnd},on:{change:t.updateFilters,input:function(e){e.target.composing||t.$set(t.localFilters,"reminderEnd",e.target.value)}}})]):t._e()]),e("div",{staticClass:"mb-4"},[e("label",{staticClass:"block text-sm font-medium text-gray-700 mb-2"},[t._v("排序")]),e("div",{staticClass:"grid grid-cols-2 gap-2"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.sort,expression:"localFilters.sort"}],staticClass:"px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",on:{change:[function(e){var a=Array.prototype.filter.call(e.target.options,function(t){return t.selected}).map(function(t){var e="_value"in t?t._value:t.value;return e});t.$set(t.localFilters,"sort",e.target.multiple?a:a[0])},t.updateFilters]}},t._l(t.sortOptions,function(a){return e("option",{key:a.value,domProps:{value:a.value}},[t._v(" "+t._s(a.label)+" ")])}),0),e("select",{directives:[{name:"model",rawName:"v-model",value:t.localFilters.order,expression:"localFilters.order"}],staticClass:"px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",on:{change:[function(e){var a=Array.prototype.filter.call(e.target.options,function(t){return t.selected}).map(function(t){var e="_value"in t?t._value:t.value;return e});t.$set(t.localFilters,"order",e.target.multiple?a:a[0])},t.updateFilters]}},t._l(t.orderOptions,function(a){return e("option",{key:a.value,domProps:{value:a.value}},[t._v(" "+t._s(a.label)+" ")])}),0)])]),t.hasActiveFilters?e("div",{staticClass:"border-t border-gray-200 pt-4"},[e("h3",{staticClass:"text-sm font-medium text-gray-700 mb-2"},[t._v("活动筛选")]),e("div",{staticClass:"flex flex-wrap gap-1"},[t.localFilters.search?e("span",{staticClass:"inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"},[t._v(" 搜索: "+t._s(t.localFilters.search)+" "),e("button",{staticClass:"ml-1 text-blue-600 hover:text-blue-800",on:{click:t.clearSearch}},[e("svg",{staticClass:"w-3 h-3",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"}})])])]):t._e(),t._l(t.localFilters.tags,function(a){return e("span",{key:`tag-${a}`,staticClass:"inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full"},[t._v(" "+t._s(a)+" "),e("button",{staticClass:"ml-1 text-green-600 hover:text-green-800",on:{click:function(e){return t.removeTag(a)}}},[e("svg",{staticClass:"w-3 h-3",attrs:{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"}},[e("path",{attrs:{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"}})])])])})],2)]):t._e()])},N=[],U={name:"FilterSidebar",props:{filters:{type:Object,required:!0},availableTags:{type:Array,default:()=>[]}},data(){return{localFilters:{search:"",tags:[],urgency:[],importance:[],reminderFilter:"",reminderStart:"",reminderEnd:"",sort:"created_at",order:"desc"},urgencyOptions:n,importanceOptions:c,sortOptions:d,orderOptions:u,reminderOptions:g}},computed:{hasActiveFilters(){return this.localFilters.search||this.localFilters.tags.length>0||this.localFilters.urgency.length>0||this.localFilters.importance.length>0||this.localFilters.reminderFilter}},watch:{filters:{handler(t){this.localFilters={...this.localFilters,...t}},immediate:!0,deep:!0}},created(){this.debouncedSearch=C(()=>{this.updateFilters()},300)},methods:{updateFilters(){this.$emit("update-filters",{...this.localFilters})},updateReminderFilter(){const t=new Date,e=t=>t.toISOString().split("T")[0];switch(this.localFilters.reminderFilter){case"today":this.localFilters.reminderStart=e(t),this.localFilters.reminderEnd=e(t);break;case"week":const a=new Date(t);a.setDate(t.getDate()-t.getDay());const s=new Date(a);s.setDate(a.getDate()+6),this.localFilters.reminderStart=e(a),this.localFilters.reminderEnd=e(s);break;case"month":const r=new Date(t.getFullYear(),t.getMonth(),1),o=new Date(t.getFullYear(),t.getMonth()+1,0);this.localFilters.reminderStart=e(r),this.localFilters.reminderEnd=e(o);break;case"custom":break;default:this.localFilters.reminderStart="",this.localFilters.reminderEnd=""}this.updateFilters()},resetFilters(){this.localFilters={search:"",tags:[],urgency:[],importance:[],reminderFilter:"",reminderStart:"",reminderEnd:"",sort:"created_at",order:"desc"},this.updateFilters()},clearSearch(){this.localFilters.search="",this.updateFilters()},removeTag(t){const e=this.localFilters.tags.indexOf(t);e>-1&&(this.localFilters.tags.splice(e,1),this.updateFilters())}}},I=U,z=(0,B.A)(I,P,N,!1,null,"fe9d39fc",null),H=z.exports,V=function(){var t=this,e=t._self._c;return e("div",{staticClass:"stats-chart bg-white rounded-lg shadow-md p-4"},[e("h3",{staticClass:"text-lg font-semibold text-gray-900 mb-4"},[t._v("统计信息")]),e("div",{staticClass:"grid grid-cols-2 gap-4 mb-6"},[e("div",{staticClass:"text-center p-3 bg-blue-50 rounded-lg"},[e("div",{staticClass:"text-2xl font-bold text-blue-600"},[t._v(t._s(t.stats.total_bookmarks||0))]),e("div",{staticClass:"text-sm text-gray-600"},[t._v("总书签数")])]),e("div",{staticClass:"text-center p-3 bg-green-50 rounded-lg"},[e("div",{staticClass:"text-2xl font-bold text-green-600"},[t._v(t._s(t.stats.tags_count||0))]),e("div",{staticClass:"text-sm text-gray-600"},[t._v("标签数")])])]),e("div",{staticClass:"mb-6"},[e("h4",{staticClass:"text-md font-medium text-gray-800 mb-2"},[t._v("标签使用分布")]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.tagChartData.length>0,expression:"tagChartData.length > 0"}],ref:"tagsChart",staticClass:"w-full h-64"}),e("div",{directives:[{name:"show",rawName:"v-show",value:0===t.tagChartData.length,expression:"tagChartData.length === 0"}],staticClass:"w-full h-64 flex items-center justify-center text-gray-500 bg-gray-50 rounded"},[t._v(" 暂无标签数据 ")])]),e("div",{staticClass:"mb-6"},[e("h4",{staticClass:"text-md font-medium text-gray-800 mb-2"},[t._v("紧迫度分布")]),e("div",{staticClass:"space-y-2"},t._l(t.stats.urgency_stats,function(a,s){return e("div",{key:s,staticClass:"flex items-center justify-between"},[e("div",{staticClass:"flex items-center"},[e("span",{staticClass:"inline-block w-3 h-3 rounded-full mr-2",style:{backgroundColor:t.getUrgencyColor(s)}}),e("span",{staticClass:"text-sm text-gray-700"},[t._v(t._s(t.getUrgencyLabel(s)))])]),e("span",{staticClass:"text-sm font-medium text-gray-900"},[t._v(t._s(a))])])}),0)]),e("div",{staticClass:"mb-4"},[e("h4",{staticClass:"text-md font-medium text-gray-800 mb-2"},[t._v("重要度分布")]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.importanceChartData.length>0,expression:"importanceChartData.length > 0"}],ref:"importanceChart",staticClass:"w-full h-48"}),e("div",{directives:[{name:"show",rawName:"v-show",value:0===t.importanceChartData.length,expression:"importanceChartData.length === 0"}],staticClass:"w-full h-48 flex items-center justify-center text-gray-500 bg-gray-50 rounded"},[t._v(" 暂无重要度数据 ")])])])},W=[],q=a(605),K={name:"StatsChart",props:{stats:{type:Object,default:()=>({})},tagStats:{type:Array,default:()=>[]}},data(){return{tagsChart:null,importanceChart:null}},computed:{tagChartData(){return this.tagStats.map(t=>({name:t.name,value:t.count}))},importanceChartData(){return this.stats.importance_stats?Object.entries(this.stats.importance_stats).map(([t,e])=>({name:`${t}星`,value:e})):[]}},watch:{tagChartData:{handler(){this.$nextTick(()=>{this.renderTagsChart()})},deep:!0},importanceChartData:{handler(){this.$nextTick(()=>{this.renderImportanceChart()})},deep:!0}},mounted(){this.$nextTick(()=>{this.initCharts()}),window.addEventListener("resize",this.handleResize)},beforeDestroy(){this.tagsChart&&this.tagsChart.dispose(),this.importanceChart&&this.importanceChart.dispose(),window.removeEventListener("resize",this.handleResize)},methods:{initCharts(){this.$refs.tagsChart&&(this.tagsChart=q.Ts(this.$refs.tagsChart),this.renderTagsChart()),this.$refs.importanceChart&&(this.importanceChart=q.Ts(this.$refs.importanceChart),this.renderImportanceChart())},renderTagsChart(){if(!this.tagsChart||0===this.tagChartData.length)return;const t={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",textStyle:{fontSize:12}},series:[{name:"标签使用",type:"pie",radius:["40%","70%"],center:["60%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:this.tagChartData,itemStyle:{borderRadius:4,borderColor:"#fff",borderWidth:2}}],color:["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#06b6d4","#84cc16","#f97316","#ec4899","#6366f1"]};this.tagsChart.setOption(t)},renderImportanceChart(){if(!this.importanceChart||0===this.importanceChartData.length)return;const t={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:this.importanceChartData.map(t=>t.name),axisTick:{alignWithLabel:!0}},yAxis:{type:"value"},series:[{name:"书签数量",type:"bar",barWidth:"60%",data:this.importanceChartData.map(t=>t.value),itemStyle:{color:new q.fA.W4(0,0,0,1,[{offset:0,color:"#fbbf24"},{offset:1,color:"#f59e0b"}]),borderRadius:[4,4,0,0]}}]};this.importanceChart.setOption(t)},handleResize(){this.tagsChart&&this.tagsChart.resize(),this.importanceChart&&this.importanceChart.resize()},getUrgencyColor(t){const e=n.find(e=>e.value===t);return e?e.color:"#6b7280"},getUrgencyLabel(t){const e=n.find(e=>e.value===t);return e?e.label:t}}},G=K,Y=(0,B.A)(G,V,W,!1,null,"8deb756a",null),X=Y.exports,J=a(335);const Q="https://catai-bookmark-api-v1-production.up.railway.app/api",Z=J.A.create({baseURL:Q,timeout:3e4,headers:{"Content-Type":"application/json"}});Z.interceptors.request.use(t=>(console.log("API Request:",t.method.toUpperCase(),t.url),t),t=>(console.error("Request Error:",t),Promise.reject(t))),Z.interceptors.response.use(t=>(console.log("API Response:",t.status,t.config.url),t.data),t=>{console.error("Response Error:",t.response?.status,t.response?.data);const e=t.response?.data?.error?.message||t.message||"请求失败";return("ECONNABORTED"===t.code||t.message.includes("timeout"))&&console.warn("API请求超时，但应用仍可正常使用"),Promise.reject({status:t.response?.status,message:e,code:t.response?.data?.error?.code})});class tt{async healthCheck(){return await Z.get("/health")}async getBookmarks(t={}){const e=new URLSearchParams;t.search&&e.append("search",t.search),t.tags&&t.tags.length>0&&e.append("tags",Array.isArray(t.tags)?t.tags.join(","):t.tags),t.urgency&&e.append("urgency",t.urgency),t.importance&&e.append("importance",t.importance),t.page&&e.append("page",t.page),t.limit&&e.append("limit",t.limit),t.sort&&e.append("sort",t.sort),t.order&&e.append("order",t.order);const a=e.toString(),s=a?`/bookmarks?${a}`:"/bookmarks";return await Z.get(s)}async createBookmark(t){return await Z.post("/bookmarks",t)}async getBookmark(t){return await Z.get(`/bookmarks/${t}`)}async updateBookmark(t,e){return await Z.put(`/bookmarks/${t}`,e)}async deleteBookmark(t){return await Z.delete(`/bookmarks/${t}`)}async batchDeleteBookmarks(t){return await Z.delete("/bookmarks/batch",{data:{ids:t}})}async batchAddTags(t,e){return await Z.post("/bookmarks/batch/tags",{bookmark_ids:t,tags:e})}async getTags(){return await Z.get("/tags")}async getStats(){return await Z.get("/stats")}async exportData(){return await Z.get("/sync/export")}async importData(t){return await Z.post("/sync/import",t)}}const et=new tt;var at=et,st={name:"App",components:{BookmarkCard:A,BookmarkForm:L,FilterSidebar:H,StatsChart:X},data(){return{bookmarks:[],availableTags:[],stats:{},loading:!1,formLoading:!1,apiHealthy:!1,filters:{search:"",tags:[],urgency:[],importance:[],reminderFilter:"",reminderStart:"",reminderEnd:"",sort:"created_at",order:"desc"},pagination:{page:m.DEFAULT_PAGE,limit:m.DEFAULT_LIMIT,total:0,pages:0},selectedBookmarks:[],selectedBookmark:null,showBookmarkForm:!1,showSyncDialog:!1,showBatchTagDialog:!1,batchTagInput:"",message:{show:!1,type:"success",text:""}}},computed:{visiblePages(){const t=this.pagination.page,e=this.pagination.pages,a=2,s=[],r=[];for(let o=Math.max(2,t-a);o<=Math.min(e-1,t+a);o++)s.push(o);return t-a>2?r.push(1,"..."):r.push(1),r.push(...s),t+a<e-1?r.push("...",e):r.push(e),r.filter((t,e,a)=>a.indexOf(t)===e&&"..."!==t)}},async created(){await this.checkApiHealth(),await this.loadInitialData()},methods:{async checkApiHealth(){try{await at.healthCheck(),this.apiHealthy=!0}catch(t){this.apiHealthy=!1,console.error("API健康检查失败:",t)}},async loadInitialData(){await Promise.all([this.loadBookmarks(),this.loadTags(),this.loadStats()])},async loadBookmarks(){this.loading=!0;try{const t={...this.filters,page:this.pagination.page,limit:this.pagination.limit},e=await at.getBookmarks(t);e.success&&(this.bookmarks=e.data.bookmarks,this.pagination={...this.pagination,total:e.data.total,pages:e.data.pages})}catch(t){this.showMessage("error",_(t))}finally{this.loading=!1}},async loadTags(){try{const t=await at.getTags();t.success&&(this.availableTags=t.data)}catch(t){console.error("加载标签失败:",t)}},async loadStats(){try{const t=await at.getStats();t.success&&(this.stats=t.data)}catch(t){console.error("加载统计信息失败:",t)}},async handleFiltersUpdate(t){this.filters={...t},this.pagination.page=1,this.selectedBookmarks=[],await this.loadBookmarks()},async changePage(t){t>=1&&t<=this.pagination.pages&&(this.pagination.page=t,await this.loadBookmarks())},selectBookmark(t){this.selectedBookmark=t,this.showBookmarkForm=!1},toggleSelectBookmark(t){const e=this.selectedBookmarks.indexOf(t.id);e>-1?this.selectedBookmarks.splice(e,1):this.selectedBookmarks.push(t.id)},async handleBookmarkSubmit(t){this.formLoading=!0;try{let e;e=this.selectedBookmark?await at.updateBookmark(this.selectedBookmark.id,t):await at.createBookmark(t),e.success&&(this.showMessage("success",this.selectedBookmark?f.UPDATE_SUCCESS:f.CREATE_SUCCESS),this.closeBookmarkForm(),await this.loadInitialData())}catch(e){this.showMessage("error",_(e))}finally{this.formLoading=!1}},async handleBookmarkDelete(t){this.formLoading=!0;try{const e=await at.deleteBookmark(t);e.success&&(this.showMessage("success",f.DELETE_SUCCESS),this.closeBookmarkForm(),await this.loadInitialData())}catch(e){this.showMessage("error",_(e))}finally{this.formLoading=!1}},async batchDeleteBookmarks(){if(confirm(`确定要删除选中的 ${this.selectedBookmarks.length} 个书签吗？`))try{const t=await at.batchDeleteBookmarks(this.selectedBookmarks);t.success&&(this.showMessage("success",f.BATCH_DELETE_SUCCESS),this.selectedBookmarks=[],await this.loadInitialData())}catch(t){this.showMessage("error",_(t))}},async handleBatchAddTags(){if(!this.batchTagInput.trim())return;const t=this.batchTagInput.split(",").map(t=>t.trim()).filter(t=>t);try{const e=await at.batchAddTags(this.selectedBookmarks,t);e.success&&(this.showMessage("success",f.BATCH_ADD_TAGS_SUCCESS),this.showBatchTagDialog=!1,this.batchTagInput="",this.selectedBookmarks=[],await this.loadInitialData())}catch(e){this.showMessage("error",_(e))}},async syncToCloud(){try{const t=await at.exportData();t.success&&(this.showMessage("success","数据已上传到云端"),this.showSyncDialog=!1)}catch(t){this.showMessage("error",_(t))}},async syncFromCloud(){try{const t=await at.exportData();t.success&&(await at.importData(t.data),this.showMessage("success","数据已从云端下载"),this.showSyncDialog=!1,await this.loadInitialData())}catch(t){this.showMessage("error",_(t))}},async exportData(){try{const t=await at.exportData();if(t.success){const e=JSON.stringify(t.data,null,2),a=new Blob([e],{type:"application/json"}),s=URL.createObjectURL(a),r=document.createElement("a");r.href=s,r.download=`bookmarks-${(new Date).toISOString().split("T")[0]}.json`,r.click(),URL.revokeObjectURL(s),this.showMessage("success",f.EXPORT_SUCCESS)}}catch(t){this.showMessage("error",_(t))}},closeBookmarkForm(){this.showBookmarkForm=!1,this.selectedBookmark=null},showMessage(t,e){this.message={show:!0,type:t,text:e},setTimeout(()=>{this.hideMessage()},3e3)},hideMessage(){this.message.show=!1}}},rt=st,ot=(0,B.A)(rt,r,o,!1,null,null,null),it=ot.exports;s.Ay.config.productionTip=!1,new s.Ay({render:t=>t(it)}).$mount("#app")}},e={};function a(s){var r=e[s];if(void 0!==r)return r.exports;var o=e[s]={exports:{}};return t[s](o,o.exports,a),o.exports}a.m=t,function(){var t=[];a.O=function(e,s,r,o){if(!s){var i=1/0;for(d=0;d<t.length;d++){s=t[d][0],r=t[d][1],o=t[d][2];for(var l=!0,n=0;n<s.length;n++)(!1&o||i>=o)&&Object.keys(a.O).every(function(t){return a.O[t](s[n])})?s.splice(n--,1):(l=!1,o<i&&(i=o));if(l){t.splice(d--,1);var c=r();void 0!==c&&(e=c)}}return e}o=o||0;for(var d=t.length;d>0&&t[d-1][2]>o;d--)t[d]=t[d-1];t[d]=[s,r,o]}}(),function(){a.d=function(t,e){for(var s in e)a.o(e,s)&&!a.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"===typeof window)return window}}()}(),function(){a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){a.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}(),function(){var t={524:0};a.O.j=function(e){return 0===t[e]};var e=function(e,s){var r,o,i=s[0],l=s[1],n=s[2],c=0;if(i.some(function(e){return 0!==t[e]})){for(r in l)a.o(l,r)&&(a.m[r]=l[r]);if(n)var d=n(a)}for(e&&e(s);c<i.length;c++)o=i[c],a.o(t,o)&&t[o]&&t[o][0](),t[o]=0;return a.O(d)},s=self["webpackChunkvue2_bookmark_manager"]=self["webpackChunkvue2_bookmark_manager"]||[];s.forEach(e.bind(null,0)),s.push=e.bind(null,s.push.bind(s))}();var s=a.O(void 0,[504],function(){return a(366)});s=a.O(s)})();