#!/usr/bin/env python3
"""
Railway Deployment Health Check Script
Tests the deployed bookmark manager API on Railway
"""

import requests
import json
import time
from datetime import datetime

# Railway deployment URL - replace with your actual Railway URL
RAILWAY_URL = "https://catai-bookmark-api-v1-production.up.railway.app"
# Alternative format: "https://your-service-name.railway.app"

class RailwayHealthChecker:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Railway-Health-Checker/1.0'
        })
    
    def print_status(self, test_name, status, message=""):
        """Print test status with formatting"""
        status_symbol = "✅" if status else "❌"
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {status_symbol} {test_name}: {message}")
    
    def test_health_endpoint(self):
        """Test the health check endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.print_status("Health Check", True, f"Status: {data.get('status', 'unknown')}")
                return True
            else:
                self.print_status("Health Check", False, f"HTTP {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.print_status("Health Check", False, f"Connection error: {str(e)}")
            return False
    
    def test_cors_headers(self):
        """Test CORS headers"""
        try:
            response = self.session.options(f"{self.base_url}/api/health", timeout=10)
            cors_headers = {
                'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
            }
            
            if any(cors_headers.values()):
                self.print_status("CORS Headers", True, "CORS configured")
                return True
            else:
                self.print_status("CORS Headers", False, "No CORS headers found")
                return False
        except requests.exceptions.RequestException as e:
            self.print_status("CORS Headers", False, f"Error: {str(e)}")
            return False
    
    def test_database_connection(self):
        """Test database connectivity through API"""
        try:
            # Test getting bookmarks (should work even if empty)
            response = self.session.get(f"{self.base_url}/api/bookmarks", timeout=15)
            if response.status_code in [200, 404]:  # 200 for data, 404 for no data
                self.print_status("Database Connection", True, "Database accessible")
                return True
            else:
                self.print_status("Database Connection", False, f"HTTP {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.print_status("Database Connection", False, f"Error: {str(e)}")
            return False
    
    def test_api_endpoints(self):
        """Test main API endpoints"""
        endpoints = [
            ("/api/bookmarks", "GET", "Get Bookmarks"),
            ("/api/folders", "GET", "Get Folders"),
            ("/api/tags", "GET", "Get Tags")
        ]
        
        results = []
        for endpoint, method, name in endpoints:
            try:
                response = self.session.request(method, f"{self.base_url}{endpoint}", timeout=10)
                if response.status_code in [200, 404]:  # Accept both success and not found
                    self.print_status(name, True, f"HTTP {response.status_code}")
                    results.append(True)
                else:
                    self.print_status(name, False, f"HTTP {response.status_code}")
                    results.append(False)
            except requests.exceptions.RequestException as e:
                self.print_status(name, False, f"Error: {str(e)}")
                results.append(False)
        
        return all(results)
    
    def test_response_time(self):
        """Test API response time"""
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/health", timeout=10)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            if response.status_code == 200 and response_time < 5000:  # Less than 5 seconds
                self.print_status("Response Time", True, f"{response_time:.2f}ms")
                return True
            else:
                self.print_status("Response Time", False, f"{response_time:.2f}ms (too slow)")
                return False
        except requests.exceptions.RequestException as e:
            self.print_status("Response Time", False, f"Error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all health checks"""
        print(f"\n🚀 Railway Deployment Health Check")
        print(f"📍 Testing URL: {self.base_url}")
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        tests = [
            ("Basic Connectivity", self.test_health_endpoint),
            ("Response Time", self.test_response_time),
            ("CORS Configuration", self.test_cors_headers),
            ("Database Connection", self.test_database_connection),
            ("API Endpoints", self.test_api_endpoints)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🔍 Running {test_name}...")
            result = test_func()
            results.append(result)
            time.sleep(1)  # Small delay between tests
        
        # Summary
        print("\n" + "=" * 60)
        passed = sum(results)
        total = len(results)
        
        if passed == total:
            print(f"🎉 All tests passed! ({passed}/{total})")
            print("✅ Your Railway deployment is healthy and ready!")
        else:
            print(f"⚠️  Some tests failed ({passed}/{total})")
            print("❌ Please check the failed tests above")
        
        print(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return passed == total

def main():
    """Main function"""
    print("Railway Deployment Health Checker")
    print("=" * 40)
    
    # You can also pass the URL as a command line argument
    import sys
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = RAILWAY_URL
    
    print(f"Testing deployment at: {url}")
    
    checker = RailwayHealthChecker(url)
    success = checker.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
