#!/usr/bin/env python3
"""
Diagnose database connection issue
"""

import requests
import json
from datetime import datetime

# API URL
RAILWAY_API = "https://catai-bookmark-api-v1-production.up.railway.app/api"

def diagnose_database():
    """Diagnose which database we're connected to"""
    
    print("🔍 Database Connection Diagnosis")
    print("=" * 50)
    
    try:
        # Step 1: Add a unique test bookmark
        test_bookmark = {
            "title": f"DIAGNOSTIC_BOOKMARK_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "url": f"https://diagnostic-test-{datetime.now().strftime('%H%M%S')}.example.com",
            "description": "This is a diagnostic bookmark to identify which database we're using",
            "tags": ["diagnostic", "test", "database-identification"],
            "folder": "DIAGNOSTIC_FOLDER",
            "urgency": 5,
            "importance": 5
        }
        
        print("1. Adding diagnostic bookmark...")
        response = requests.post(
            f"{RAILWAY_API}/bookmarks",
            json=test_bookmark,
            timeout=15
        )
        
        if response.status_code in [200, 201]:
            result = response.json()
            bookmark_id = result.get('data', {}).get('_id', 'Unknown')
            print(f"   ✅ Diagnostic bookmark added successfully")
            print(f"   🆔 Bookmark ID: {bookmark_id}")
            print(f"   📝 Title: {test_bookmark['title']}")
            print(f"   🔗 URL: {test_bookmark['url']}")
        else:
            print(f"   ❌ Failed to add diagnostic bookmark: HTTP {response.status_code}")
            return False
        
        # Step 2: Get current database stats
        print(f"\n2. Current database statistics...")
        stats_response = requests.get(f"{RAILWAY_API}/stats", timeout=15)
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"   📊 Stats: {json.dumps(stats, indent=4, ensure_ascii=False)}")
        
        # Step 3: Get all bookmarks to see what's in current database
        print(f"\n3. Current database contents...")
        bookmarks_response = requests.get(f"{RAILWAY_API}/bookmarks?limit=100", timeout=15)
        
        if bookmarks_response.status_code == 200:
            data = bookmarks_response.json()
            bookmarks = data.get('data', {}).get('bookmarks', [])
            total = data.get('data', {}).get('total', 0)
            
            print(f"   📚 Total bookmarks in current database: {total}")
            
            if bookmarks:
                print(f"   📋 All bookmarks in current database:")
                for i, bookmark in enumerate(bookmarks, 1):
                    title = bookmark.get('title', 'No title')
                    url = bookmark.get('url', 'No URL')
                    created = bookmark.get('created_at', 'Unknown date')
                    print(f"      {i:2d}. {title}")
                    print(f"          URL: {url}")
                    print(f"          Created: {created}")
                    print()
            else:
                print(f"   📭 No bookmarks found in current database")
        
        # Step 4: Export current database for comparison
        print(f"\n4. Exporting current database...")
        export_response = requests.get(f"{RAILWAY_API}/sync/export", timeout=30)
        
        if export_response.status_code == 200:
            export_data = export_response.json()
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"current_database_diagnosis_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"   💾 Current database exported to: {filename}")
            
            # Show summary
            if 'data' in export_data:
                bookmarks = export_data['data'].get('bookmarks', [])
                print(f"   📊 Exported {len(bookmarks)} bookmarks")
        
        print(f"\n" + "=" * 50)
        print(f"🎯 DIAGNOSIS COMPLETE")
        print(f"📝 Next Steps:")
        print(f"   1. Check your MongoDB to see which database contains the diagnostic bookmark:")
        print(f"      Title: {test_bookmark['title']}")
        print(f"      URL: {test_bookmark['url']}")
        print(f"   2. This will tell you which database the API is currently using")
        print(f"   3. Compare with your original database to see the difference")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main function"""
    success = diagnose_database()
    
    if success:
        print(f"\n💡 Tips:")
        print(f"   - Look for the diagnostic bookmark in your MongoDB")
        print(f"   - Compare the contents of both databases")
        print(f"   - The database with the diagnostic bookmark is the one currently connected")
    
    return success

if __name__ == "__main__":
    main()
