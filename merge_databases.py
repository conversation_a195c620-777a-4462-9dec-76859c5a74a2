#!/usr/bin/env python3
"""
Merge two CataPage_test_wwtmw20_Chrome databases
"""

import requests
import json
import time
from datetime import datetime

# API URL
RAILWAY_API = "https://catai-bookmark-api-v1-production.up.railway.app/api"

class DatabaseMerger:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Database-Merger/1.0'
        })
    
    def export_current_data(self):
        """Export all data from current database via API"""
        print("📤 Exporting current database data...")
        
        try:
            # Export via API
            response = self.session.get(f"{RAILWAY_API}/sync/export", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Export successful")
                
                # Save to file
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"database_export_{timestamp}.json"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                print(f"   💾 Data saved to: {filename}")
                
                # Show summary
                if 'data' in data:
                    bookmarks = data['data'].get('bookmarks', [])
                    print(f"   📊 Exported {len(bookmarks)} bookmarks")
                    
                    # Show sample bookmarks
                    if bookmarks:
                        print("   📚 Sample bookmarks:")
                        for bookmark in bookmarks[:3]:
                            print(f"      - {bookmark.get('title', 'No title')}")
                
                return data, filename
            else:
                print(f"   ❌ Export failed: HTTP {response.status_code}")
                print(f"   Response: {response.text}")
                return None, None
                
        except Exception as e:
            print(f"   ❌ Export error: {str(e)}")
            return None, None
    
    def get_database_stats(self):
        """Get current database statistics"""
        try:
            response = self.session.get(f"{RAILWAY_API}/stats", timeout=15)
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def clear_current_database(self):
        """Clear current database (optional step)"""
        print("🗑️  Clearing current database...")
        
        try:
            # Get all bookmarks
            response = self.session.get(f"{RAILWAY_API}/bookmarks?limit=1000", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                bookmarks = data.get('data', {}).get('bookmarks', [])
                
                if not bookmarks:
                    print("   ✅ Database is already empty")
                    return True
                
                # Delete all bookmarks
                bookmark_ids = [bookmark['_id'] for bookmark in bookmarks if '_id' in bookmark]
                
                if bookmark_ids:
                    delete_response = self.session.delete(
                        f"{RAILWAY_API}/bookmarks/batch",
                        json={'ids': bookmark_ids},
                        timeout=30
                    )
                    
                    if delete_response.status_code == 200:
                        print(f"   ✅ Deleted {len(bookmark_ids)} bookmarks")
                        return True
                    else:
                        print(f"   ❌ Failed to delete bookmarks: HTTP {delete_response.status_code}")
                        return False
                else:
                    print("   ⚠️  No bookmark IDs found")
                    return True
            else:
                print(f"   ❌ Failed to get bookmarks: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Clear error: {str(e)}")
            return False
    
    def import_merged_data(self, merged_data):
        """Import merged data to current database"""
        print("📥 Importing merged data...")
        
        try:
            response = self.session.post(
                f"{RAILWAY_API}/sync/import",
                json=merged_data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Import successful")
                print(f"   📊 Import result: {result}")
                return True
            else:
                print(f"   ❌ Import failed: HTTP {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ Import error: {str(e)}")
            return False
    
    def merge_data_files(self, file1, file2=None):
        """Merge data from two export files"""
        print("🔄 Merging database files...")
        
        try:
            # Load first file
            with open(file1, 'r', encoding='utf-8') as f:
                data1 = json.load(f)
            
            bookmarks1 = data1.get('data', {}).get('bookmarks', [])
            print(f"   📚 File 1: {len(bookmarks1)} bookmarks")
            
            if file2:
                # Load second file
                with open(file2, 'r', encoding='utf-8') as f:
                    data2 = json.load(f)
                
                bookmarks2 = data2.get('data', {}).get('bookmarks', [])
                print(f"   📚 File 2: {len(bookmarks2)} bookmarks")
                
                # Merge bookmarks (remove duplicates by URL)
                merged_bookmarks = []
                seen_urls = set()
                
                # Add bookmarks from both sources
                for bookmark in bookmarks1 + bookmarks2:
                    url = bookmark.get('url', '')
                    if url and url not in seen_urls:
                        seen_urls.add(url)
                        merged_bookmarks.append(bookmark)
                    elif not url:  # Keep bookmarks without URL
                        merged_bookmarks.append(bookmark)
                
                print(f"   🔗 Merged: {len(merged_bookmarks)} unique bookmarks")
            else:
                merged_bookmarks = bookmarks1
                print(f"   📝 Using single file: {len(merged_bookmarks)} bookmarks")
            
            # Create merged data structure
            merged_data = {
                "bookmarks": merged_bookmarks,
                "metadata": {
                    "export_date": datetime.now().isoformat(),
                    "source": "database_merger",
                    "total_bookmarks": len(merged_bookmarks)
                }
            }
            
            # Save merged data
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            merged_filename = f"merged_database_{timestamp}.json"
            
            with open(merged_filename, 'w', encoding='utf-8') as f:
                json.dump(merged_data, f, indent=2, ensure_ascii=False)
            
            print(f"   💾 Merged data saved to: {merged_filename}")
            
            return merged_data, merged_filename
            
        except Exception as e:
            print(f"   ❌ Merge error: {str(e)}")
            return None, None

def main():
    """Main merge process"""
    print("🔄 Database Merger for CataPage_test_wwtmw20_Chrome")
    print("=" * 60)
    
    merger = DatabaseMerger()
    
    # Step 1: Show current database stats
    print("1. Current database status:")
    stats = merger.get_database_stats()
    if stats:
        print(f"   📊 Current stats: {json.dumps(stats, indent=2, ensure_ascii=False)}")
    
    # Step 2: Export current database
    print("\n2. Exporting current database:")
    current_data, export_file = merger.export_current_data()
    
    if not current_data:
        print("❌ Failed to export current database")
        return False
    
    # Step 3: Ask user for the other database export
    print(f"\n3. Manual step required:")
    print(f"   📝 You need to provide the export file from the other database")
    print(f"   💡 Options:")
    print(f"      A) If you have another export file, place it in this directory")
    print(f"      B) Or we can proceed with just the current database data")
    
    # For now, let's proceed with current data and prepare for manual merge
    print(f"\n4. Preparing merge data:")
    
    # Create a template for manual merge
    merge_template = {
        "instructions": "Add bookmarks from your other database to the 'additional_bookmarks' array",
        "current_database_export": export_file,
        "additional_bookmarks": [],
        "example_bookmark": {
            "title": "Example Bookmark",
            "url": "https://example.com",
            "description": "Description here",
            "tags": ["tag1", "tag2"],
            "folder": "folder_name",
            "urgency": 3,
            "importance": 4
        }
    }
    
    template_file = "merge_template.json"
    with open(template_file, 'w', encoding='utf-8') as f:
        json.dump(merge_template, f, indent=2, ensure_ascii=False)
    
    print(f"   📝 Created merge template: {template_file}")
    print(f"   💡 Edit this file to add bookmarks from your other database")
    
    # Step 5: Offer to proceed with current data
    print(f"\n5. Next steps:")
    print(f"   Option A: Edit {template_file} and run the import")
    print(f"   Option B: Proceed with current database only")
    
    return True

if __name__ == "__main__":
    main()
