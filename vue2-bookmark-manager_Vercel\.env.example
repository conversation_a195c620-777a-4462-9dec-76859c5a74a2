# Vue2 Bookmark Manager Environment Variables
# Copy this file to .env.local and fill in your values

# API Configuration
VUE_APP_API_BASE_URL=http://localhost:5000/api
VUE_APP_API_TIMEOUT=10000

# Application Configuration
VUE_APP_TITLE=书签管理器
VUE_APP_VERSION=1.0.0

# Feature Flags
VUE_APP_ENABLE_ANALYTICS=false
VUE_APP_ENABLE_PWA=false

# Development Configuration
VUE_APP_DEBUG=false
VUE_APP_LOG_LEVEL=info

# External Services (if needed)
# VUE_APP_GOOGLE_ANALYTICS_ID=
# VUE_APP_SENTRY_DSN=
