#!/usr/bin/env python3
"""
Test database connection and list collections
"""

import requests
import json

# API URLs
RAILWAY_API = "https://catai-bookmark-api-v1-production.up.railway.app/api"

def test_database_info():
    """Test database connection and get info"""
    
    print("🔍 Testing Database Connection")
    print("=" * 50)
    
    try:
        # Test health endpoint
        print("1. Testing health endpoint...")
        response = requests.get(f"{RAILWAY_API}/health", timeout=15)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"   ✅ Health: {health_data}")
        else:
            print(f"   ❌ Health check failed: HTTP {response.status_code}")
            return False
        
        # Test stats endpoint (shows database info)
        print("\n2. Testing stats endpoint...")
        response = requests.get(f"{RAILWAY_API}/stats", timeout=15)
        
        if response.status_code == 200:
            stats_data = response.json()
            print(f"   ✅ Stats: {json.dumps(stats_data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ Stats failed: HTTP {response.status_code}")
        
        # Test bookmarks endpoint
        print("\n3. Testing bookmarks endpoint...")
        response = requests.get(f"{RAILWAY_API}/bookmarks", timeout=15)
        
        if response.status_code == 200:
            bookmarks_data = response.json()
            total_bookmarks = bookmarks_data.get('data', {}).get('total', 0)
            print(f"   ✅ Bookmarks: {total_bookmarks} total bookmarks found")
            
            if total_bookmarks > 0:
                bookmarks = bookmarks_data.get('data', {}).get('bookmarks', [])
                print(f"   📚 Recent bookmarks:")
                for bookmark in bookmarks[:3]:  # Show first 3
                    print(f"      - {bookmark.get('title', 'No title')}: {bookmark.get('url', 'No URL')}")
        else:
            print(f"   ❌ Bookmarks failed: HTTP {response.status_code}")
        
        # Add a test bookmark to see which database it goes to
        print("\n4. Adding test bookmark...")
        test_bookmark = {
            "title": "Database Test Bookmark",
            "url": "https://test.example.com",
            "description": "Test bookmark to verify database connection",
            "tags": ["test", "database"],
            "folder": "测试文件夹"
        }
        
        response = requests.post(
            f"{RAILWAY_API}/bookmarks",
            json=test_bookmark,
            timeout=15
        )
        
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"   ✅ Test bookmark added successfully")
            print(f"   📝 Bookmark ID: {result.get('data', {}).get('_id', 'Unknown')}")
            
            # Verify it was added
            response = requests.get(f"{RAILWAY_API}/bookmarks", timeout=15)
            if response.status_code == 200:
                updated_data = response.json()
                new_total = updated_data.get('data', {}).get('total', 0)
                print(f"   📊 Total bookmarks after adding: {new_total}")
        else:
            print(f"   ❌ Failed to add test bookmark: HTTP {response.status_code}")
            print(f"   Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main function"""
    print("📊 Database Connection Test")
    print("🎯 Purpose: Verify which database the API is connecting to")
    print()
    
    success = test_database_info()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ Database connection test completed!")
        print("📝 Please check your MongoDB to see which database received the test bookmark.")
        print("🔍 Look for 'Database Test Bookmark' in your collections.")
    else:
        print("\n❌ Database connection test failed!")
    
    return success

if __name__ == "__main__":
    main()
