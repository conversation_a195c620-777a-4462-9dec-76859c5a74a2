# 书签管理器前端产品需求文档 (Frontend PRD)

## 1. 引言

### 1.1 项目背景

本项目旨在为用户提供一个功能完善的书签管理工具，通过直观的用户界面，帮助用户高效地组织、搜索和管理个人书签。后端API已基于Python Flask和MongoDB实现，本PRD将详细描述前端应用的需求和功能。

### 1.2 项目目标

- 提供一个用户友好的界面，方便用户进行书签的增删改查。
- 实现强大的书签筛选、搜索和分类功能。
- 支持书签数据的导入导出，方便用户进行数据迁移和备份。
- 与后端API无缝集成，确保数据同步和一致性。

### 1.3 目标用户

- 需要管理大量书签的个人用户。
- 希望通过标签、分类等方式高效组织书签的用户。
- 对数据安全和备份有需求的用户。

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 书签列表展示
- **描述**: 用户进入应用后，默认展示所有书签列表。
- **数据来源**: 调用后端 `GET /api/bookmarks` 接口。
- **展示内容**: 书签标题、URL、标签、紧迫度、重要度、提醒日期、备注。
- **交互**: 点击书签卡片可查看/编辑详情。

#### 2.1.2 新增书签
- **描述**: 用户可以通过点击“新增书签”按钮，填写表单创建新书签。
- **数据提交**: 调用后端 `POST /api/bookmarks` 接口。
- **表单字段**: 标题 (必填)、URL (必填)、标签 (多选/输入)、紧迫度 (单选: 高/中/低)、重要度 (星级: 1-5)、提醒日期 (日期选择)、备注 (文本域)。
- **验证**: 标题和URL不能为空。

#### 2.1.3 编辑书签
- **描述**: 用户点击书签卡片后，右侧详情面板展示书签详情，可进行编辑。
- **数据获取**: 调用后端 `GET /api/bookmarks/{id}` 接口获取单个书签详情。
- **数据更新**: 调用后端 `PUT /api/bookmarks/{id}` 接口提交修改。
- **交互**: 实时更新表单内容，保存按钮提交修改。

#### 2.1.4 删除书签
- **描述**: 用户可在书签详情面板或书签列表中删除单个书签。
- **数据删除**: 调用后端 `DELETE /api/bookmarks/{id}` 接口。
- **交互**: 删除前需二次确认。

### 2.2 筛选与搜索

#### 2.2.1 关键词搜索
- **描述**: 用户可在左侧搜索框输入关键词，对书签标题、URL、备注进行模糊搜索。
- **数据来源**: 调用后端 `GET /api/bookmarks?search={keyword}` 接口。
- **交互**: 输入即时搜索或点击搜索按钮触发。

#### 2.2.2 标签筛选
- **描述**: 用户可在左侧边栏选择一个或多个标签进行筛选。
- **数据来源**: 调用后端 `GET /api/bookmarks?tags={tag1},{tag2}` 接口。
- **交互**: 复选框选择，支持多选，选择后自动更新书签列表。

#### 2.2.3 紧迫度筛选
- **描述**: 用户可按“高”、“中”、“低”紧迫度筛选书签。
- **数据来源**: 调用后端 `GET /api/bookmarks?urgency={level}` 接口。
- **交互**: 复选框选择，支持多选。

#### 2.2.4 重要度筛选
- **描述**: 用户可按星级（1-5星）筛选书签。
- **数据来源**: 调用后端 `GET /api/bookmarks?importance={level}` 接口。
- **交互**: 复选框选择，支持多选。

#### 2.2.5 提醒日期筛选
- **描述**: 用户可按“今天”、“本周”、“本月”或自定义日期范围筛选书签。
- **数据来源**: 调用后端 `GET /api/bookmarks?reminder_start={date}&reminder_end={date}` 接口。
- **交互**: 复选框选择或日期选择器。

#### 2.2.6 重置筛选
- **描述**: 用户可点击按钮清除所有筛选条件，恢复默认书签列表。
- **交互**: 点击“重置所有筛选”按钮。

### 2.3 标签管理与统计

#### 2.3.1 标签统计图
- **描述**: 右侧边栏未选择书签时，展示标签使用统计饼图。
- **数据来源**: 调用后端 `GET /api/tags` 接口。
- **展示内容**: 各标签的名称和使用次数。
- **技术**: 使用ECharts或其他图表库实现。

#### 2.3.2 批量添加标签
- **描述**: 用户可选择多个书签，批量添加标签。
- **数据提交**: 调用后端 `POST /api/bookmarks/batch/tags` 接口。
- **交互**: 选中书签后，点击“批量添加标签”按钮，弹出输入框。

### 2.4 数据同步

#### 2.4.1 数据上传到云端
- **描述**: 用户可将本地书签数据上传到云端（即后端数据库）。
- **数据提交**: 调用后端 `GET /api/sync/export` 获取所有数据，然后调用 `POST /api/sync/import` 接口。
- **交互**: 弹出同步对话框，选择“上传到云端”，显示进度和状态。

#### 2.4.2 从云端下载数据
- **描述**: 用户可从云端（即后端数据库）下载书签数据到本地。
- **数据获取**: 调用后端 `GET /api/sync/export` 接口。
- **数据导入**: 调用后端 `POST /api/sync/import` 接口。
- **交互**: 弹出同步对话框，选择“从云端下载”，显示进度和状态。

### 2.5 统计信息

#### 2.5.1 仪表板统计
- **描述**: 在合适的区域（例如右侧边栏或单独的统计页面），展示书签总数、标签总数、紧迫度分布、重要度分布等。
- **数据来源**: 调用后端 `GET /api/stats` 接口。

## 3. 非功能性需求

### 3.1 性能
- **加载速度**: 书签列表加载应在2秒内完成。
- **响应速度**: 用户操作（如新增、编辑、删除）响应应在500ms内完成。
- **数据量**: 支持至少10000条书签数据的流畅操作。

### 3.2 可用性与用户体验 (UX)
- **界面直观**: 布局清晰，操作流程符合用户习惯。
- **响应式设计**: 适应不同屏幕尺寸（桌面、平板、移动设备）。
- **错误提示**: 友好的错误提示和操作反馈。
- **加载状态**: 操作进行时显示加载动画或进度条。

### 3.3 兼容性
- **浏览器**: 支持Chrome、Firefox、Safari、Edge最新稳定版。
- **Vue版本**: Vue 2.x

### 3.4 安全性
- **API通信**: 所有与后端API的通信应通过HTTP/HTTPS进行。
- **数据验证**: 前端应对用户输入进行初步验证，减少无效请求。
- **错误处理**: 避免在前端暴露敏感的后端错误信息。

### 3.5 可维护性
- **代码结构**: 遵循Vue最佳实践，模块化、组件化开发。
- **命名规范**: 统一的变量、函数、组件命名规范。
- **注释**: 关键代码和复杂逻辑应有详细注释。

## 4. 技术选型

- **前端框架**: Vue 2.x
- **UI框架/库**: Tailwind CSS (已在现有代码中使用)
- **HTTP请求**: Axios
- **图表库**: ECharts (已在现有代码中使用)
- **路由**: Vue Router (如果需要多页面)
- **状态管理**: Vuex (如果应用复杂)

## 5. 界面原型 (参考现有Vue代码)

- **顶部导航栏**: 包含应用标题、同步数据、导出、用户头像等。
- **左侧边栏**: 搜索框、标签筛选、紧迫度筛选、重要度筛选、提醒日期筛选、重置筛选按钮。
- **中间内容区**: 操作栏（新增、批量添加标签、删除选中）、书签统计、书签卡片网格、加载更多按钮。
- **右侧边栏**: 
    - **书签详情面板**: 标题、URL、标签、紧迫度、重要度、提醒日期、备注的编辑表单，保存/删除按钮。
    - **未选择书签时**: 提示信息、新增书签按钮、标签使用统计饼图。

## 6. 后端API接口列表 (已在后端API设计文档中详细说明)

- `GET /api/health`
- `GET /api/bookmarks`
- `POST /api/bookmarks`
- `GET /api/bookmarks/{id}`
- `PUT /api/bookmarks/{id}`
- `DELETE /api/bookmarks/{id}`
- `DELETE /api/bookmarks/batch`
- `POST /api/bookmarks/batch/tags`
- `GET /api/tags`
- `GET /api/stats`
- `GET /api/sync/export`
- `POST /api/sync/import`

## 7. 迭代计划

### 阶段1: 基础功能实现
- 书签列表展示
- 新增、编辑、删除单个书签
- 关键词搜索
- 标签筛选
- 健康检查API集成

### 阶段2: 完善功能
- 紧迫度、重要度、提醒日期筛选
- 标签统计图表展示
- 批量添加标签
- 统计信息展示

### 阶段3: 数据同步与优化
- 数据导入导出功能
- 性能优化和用户体验提升
- 响应式布局调整

## 8. 风险与挑战

- **API响应时间**: 确保后端API在数据量大时仍能快速响应。
- **数据一致性**: 确保前端与后端数据同步的准确性。
- **用户体验**: 复杂的筛选逻辑可能影响用户体验，需优化交互。

## 9. 附录

- 后端API设计文档
- 现有Vue前端代码（作为参考）

---

