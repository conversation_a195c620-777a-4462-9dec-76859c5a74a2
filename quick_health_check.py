#!/usr/bin/env python3
"""
Quick Railway Health Check
Simple script to quickly test if the Railway deployment is working
"""

import requests
import json

# Replace this with your actual Railway URL
RAILWAY_URL = "https://catai-bookmark-api-v1-production.up.railway.app"

def quick_test():
    """Quick health check"""
    print("🚀 Quick Railway Health Check")
    print(f"📍 Testing: {RAILWAY_URL}")
    print("-" * 50)
    
    try:
        # Test health endpoint
        print("1. Testing health endpoint...")
        response = requests.get(f"{RAILWAY_URL}/api/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Health check passed: {data}")
        else:
            print(f"   ❌ Health check failed: HTTP {response.status_code}")
            return False
        
        # Test bookmarks endpoint
        print("2. Testing bookmarks endpoint...")
        response = requests.get(f"{RAILWAY_URL}/api/bookmarks", timeout=10)
        
        if response.status_code in [200, 404]:
            print(f"   ✅ Bookmarks endpoint accessible: HTTP {response.status_code}")
        else:
            print(f"   ❌ Bookmarks endpoint failed: HTTP {response.status_code}")
            return False
        
        # Test CORS
        print("3. Testing CORS...")
        response = requests.options(f"{RAILWAY_URL}/api/health", timeout=10)
        cors_origin = response.headers.get('Access-Control-Allow-Origin')
        
        if cors_origin:
            print(f"   ✅ CORS configured: {cors_origin}")
        else:
            print(f"   ⚠️  CORS headers not found")
        
        print("\n🎉 Quick health check completed successfully!")
        print(f"🌐 Your API is live at: {RAILWAY_URL}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Connection error: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    if not success:
        print("\n❌ Health check failed. Please check your Railway deployment.")
        exit(1)
    else:
        print("\n✅ All checks passed! Your Railway deployment is healthy.")
        exit(0)
