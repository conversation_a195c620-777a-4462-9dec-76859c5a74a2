<!doctype html><html lang="zh-CN"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="/favicon.ico"><title>书签管理器 - Vue2</title><meta name="description" content="基于Vue2的书签管理器，支持标签分类、搜索筛选、数据同步等功能"><meta name="keywords" content="书签管理,Vue2,标签分类,搜索筛选"><meta name="author" content="Bookmark Manager"><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"><script src="https://cdn.tailwindcss.com"></script><script>tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'system-ui', 'sans-serif'],
            },
            colors: {
              primary: {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a',
              }
            },
            animation: {
              'fade-in': 'fadeIn 0.5s ease-in-out',
              'slide-up': 'slideUp 0.3s ease-out',
              'bounce-in': 'bounceIn 0.6s ease-out',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0' },
                '100%': { opacity: '1' },
              },
              slideUp: {
                '0%': { transform: 'translateY(10px)', opacity: '0' },
                '100%': { transform: 'translateY(0)', opacity: '1' },
              },
              bounceIn: {
                '0%': { transform: 'scale(0.3)', opacity: '0' },
                '50%': { transform: 'scale(1.05)' },
                '70%': { transform: 'scale(0.9)' },
                '100%': { transform: 'scale(1)', opacity: '1' },
              },
            }
          }
        }
      }</script><style>/* 自定义CSS变量 */
      :root {
        --primary-color: #3b82f6;
        --secondary-color: #6b7280;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --background-color: #f9fafb;
        --surface-color: #ffffff;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --border-color: #e5e7eb;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }
      
      /* 基础样式重置 */
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        background-color: var(--background-color);
        color: var(--text-primary);
        line-height: 1.6;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--background-color);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 隐藏加载动画 */
      .loaded .loading-container {
        display: none;
      }
      
      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }
      
      /* 选择文本样式 */
      ::selection {
        background-color: rgba(59, 130, 246, 0.2);
        color: var(--text-primary);
      }
      
      /* 焦点样式 */
      :focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
      }
      
      /* 无障碍支持 */
      @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
      
      /* 打印样式 */
      @media print {
        body {
          background: white;
          color: black;
        }
        
        .no-print {
          display: none !important;
        }
      }</style><script defer="defer" src="/static/js/chunk-vendors.7ca7a11f.js"></script><script defer="defer" src="/static/js/app.691c805c.js"></script><link href="/static/css/app.fe094c5f.css" rel="stylesheet"></head><body><noscript><div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;"><h2>需要启用JavaScript</h2><p>此应用需要JavaScript才能正常运行，请在浏览器中启用JavaScript。</p><p>This app requires JavaScript to run properly. Please enable JavaScript in your browser.</p></div></noscript><div class="loading-container"><div class="text-center"><div class="loading-spinner"></div><p class="mt-4 text-gray-600">加载中...</p></div></div><div id="app"></div><script>window.addEventListener('load', function() {
        document.body.classList.add('loaded');
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('页面错误:', e.error);
      });
      
      // 未处理的Promise错误
      window.addEventListener('unhandledrejection', function(e) {
        console.error('未处理的Promise错误:', e.reason);
      });</script></body></html>