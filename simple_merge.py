#!/usr/bin/env python3
"""
Simple database merge - Export current data and prepare for manual merge
"""

import requests
import json
from datetime import datetime

# API URL
RAILWAY_API = "https://catai-bookmark-api-v1-production.up.railway.app/api"

def export_and_prepare():
    """Export current database and prepare for merge"""
    
    print("🔄 Simple Database Merge Tool")
    print("=" * 40)
    
    try:
        # Step 1: Export current database
        print("1. Exporting current database...")
        response = requests.get(f"{RAILWAY_API}/sync/export", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # Save current export
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            current_file = f"current_database_{timestamp}.json"
            
            with open(current_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ Current database exported to: {current_file}")
            
            # Show summary
            if 'data' in data:
                bookmarks = data['data'].get('bookmarks', [])
                print(f"   📊 Found {len(bookmarks)} bookmarks in current database")
                
                if bookmarks:
                    print("   📚 Sample bookmarks:")
                    for i, bookmark in enumerate(bookmarks[:5]):
                        print(f"      {i+1}. {bookmark.get('title', 'No title')} - {bookmark.get('url', 'No URL')}")
            
            # Step 2: Create manual merge instructions
            print(f"\n2. Creating merge instructions...")
            
            instructions = {
                "merge_instructions": {
                    "step_1": "This file contains your current database export",
                    "step_2": "To merge with another database:",
                    "step_2a": "1. Export data from your other database",
                    "step_2b": "2. Copy bookmarks from other database to 'additional_bookmarks' below",
                    "step_2c": "3. Run the import command",
                    "step_3": "The system will automatically remove duplicates based on URL"
                },
                "current_database": {
                    "export_date": datetime.now().isoformat(),
                    "bookmark_count": len(data.get('data', {}).get('bookmarks', [])),
                    "bookmarks": data.get('data', {}).get('bookmarks', [])
                },
                "additional_bookmarks": {
                    "instructions": "Add bookmarks from your other database here",
                    "format": "Same format as current_database.bookmarks",
                    "bookmarks": []
                }
            }
            
            merge_file = f"merge_ready_{timestamp}.json"
            with open(merge_file, 'w', encoding='utf-8') as f:
                json.dump(instructions, f, indent=2, ensure_ascii=False)
            
            print(f"   📝 Merge file created: {merge_file}")
            
            # Step 3: Show next steps
            print(f"\n3. Next Steps:")
            print(f"   📋 Manual Steps:")
            print(f"      1. Open {merge_file}")
            print(f"      2. Add bookmarks from your other database to 'additional_bookmarks.bookmarks'")
            print(f"      3. Save the file")
            print(f"      4. Run: python import_merged.py {merge_file}")
            
            print(f"\n   🔧 Alternative - Direct API approach:")
            print(f"      1. Get data from other database manually")
            print(f"      2. Use the import API endpoint directly")
            
            return True
            
        else:
            print(f"   ❌ Export failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    export_and_prepare()
