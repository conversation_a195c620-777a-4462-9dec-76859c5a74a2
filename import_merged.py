#!/usr/bin/env python3
"""
Import merged database data
"""

import requests
import json
import sys
from datetime import datetime

# API URL
RAILWAY_API = "https://catai-bookmark-api-v1-production.up.railway.app/api"

def import_merged_data(filename):
    """Import merged data from file"""
    
    print("📥 Database Import Tool")
    print("=" * 30)
    
    try:
        # Load merge file
        print(f"1. Loading merge file: {filename}")
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract bookmarks
        current_bookmarks = data.get('current_database', {}).get('bookmarks', [])
        additional_bookmarks = data.get('additional_bookmarks', {}).get('bookmarks', [])
        
        print(f"   📚 Current database: {len(current_bookmarks)} bookmarks")
        print(f"   📚 Additional bookmarks: {len(additional_bookmarks)} bookmarks")
        
        # Merge and deduplicate
        print(f"\n2. Merging and deduplicating...")
        merged_bookmarks = []
        seen_urls = set()
        
        all_bookmarks = current_bookmarks + additional_bookmarks
        
        for bookmark in all_bookmarks:
            url = bookmark.get('url', '').strip()
            title = bookmark.get('title', '').strip()
            
            # Create a unique key (URL or title if no URL)
            unique_key = url if url else title
            
            if unique_key and unique_key not in seen_urls:
                seen_urls.add(unique_key)
                merged_bookmarks.append(bookmark)
            elif not unique_key:
                # Keep bookmarks without URL or title (rare case)
                merged_bookmarks.append(bookmark)
        
        print(f"   🔗 Merged result: {len(merged_bookmarks)} unique bookmarks")
        
        # Prepare import data
        import_data = {
            "bookmarks": merged_bookmarks,
            "metadata": {
                "import_date": datetime.now().isoformat(),
                "source": "manual_merge",
                "original_count": len(all_bookmarks),
                "deduplicated_count": len(merged_bookmarks)
            }
        }
        
        # Step 3: Clear current database (optional)
        print(f"\n3. Database preparation...")
        clear_choice = input("   ❓ Clear current database before import? (y/N): ").strip().lower()
        
        if clear_choice == 'y':
            print("   🗑️  Clearing current database...")
            
            # Get all current bookmarks
            response = requests.get(f"{RAILWAY_API}/bookmarks?limit=1000", timeout=30)
            if response.status_code == 200:
                current_data = response.json()
                current_bookmarks_api = current_data.get('data', {}).get('bookmarks', [])
                
                if current_bookmarks_api:
                    bookmark_ids = [b['_id'] for b in current_bookmarks_api if '_id' in b]
                    
                    if bookmark_ids:
                        delete_response = requests.delete(
                            f"{RAILWAY_API}/bookmarks/batch",
                            json={'ids': bookmark_ids},
                            timeout=30
                        )
                        
                        if delete_response.status_code == 200:
                            print(f"      ✅ Cleared {len(bookmark_ids)} existing bookmarks")
                        else:
                            print(f"      ⚠️  Failed to clear some bookmarks")
                else:
                    print("      ✅ Database was already empty")
        
        # Step 4: Import merged data
        print(f"\n4. Importing merged data...")
        
        response = requests.post(
            f"{RAILWAY_API}/sync/import",
            json=import_data,
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Import successful!")
            print(f"   📊 Result: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # Verify import
            print(f"\n5. Verifying import...")
            verify_response = requests.get(f"{RAILWAY_API}/bookmarks", timeout=15)
            
            if verify_response.status_code == 200:
                verify_data = verify_response.json()
                total_after = verify_data.get('data', {}).get('total', 0)
                print(f"   ✅ Verification: {total_after} bookmarks now in database")
                
                # Show sample
                bookmarks_after = verify_data.get('data', {}).get('bookmarks', [])
                if bookmarks_after:
                    print(f"   📚 Sample bookmarks:")
                    for i, bookmark in enumerate(bookmarks_after[:5]):
                        print(f"      {i+1}. {bookmark.get('title', 'No title')}")
            
            return True
            
        else:
            print(f"   ❌ Import failed: HTTP {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python import_merged.py <merge_file.json>")
        return False
    
    filename = sys.argv[1]
    return import_merged_data(filename)

if __name__ == "__main__":
    main()
